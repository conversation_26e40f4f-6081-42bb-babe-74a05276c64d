#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
个性化偏好引擎
基于用户行为学习和推荐优化
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from app.logger import logger


class PreferenceEngine:
    """个性化偏好学习和推荐引擎"""
    
    def __init__(self):
        self.user_profiles = {}
        self.session_data = {}
        self.recommendation_history = {}
    
    def get_user_id(self, locations: List[str], group_size: int) -> str:
        """基于位置和群组生成用户标识"""
        # 使用位置和群组大小生成匿名用户ID
        identifier = f"{sorted(locations)}_{group_size}"
        return hashlib.md5(identifier.encode()).hexdigest()[:8]
    
    def learn_from_selection(self, user_id: str, selected_place: Dict, context: Dict):
        """从用户选择中学习偏好"""
        if user_id not in self.user_profiles:
            self.user_profiles[user_id] = {
                "preferences": {
                    "venue_types": {},
                    "scenarios": {},
                    "features": {},
                    "price_range": {},
                    "distance_tolerance": []
                },
                "interaction_count": 0,
                "last_updated": datetime.now(),
                "behavior_patterns": {}
            }
        
        profile = self.user_profiles[user_id]
        profile["interaction_count"] += 1
        profile["last_updated"] = datetime.now()
        
        # 学习场所类型偏好
        venue_type = context.get("keywords", "其他")
        profile["preferences"]["venue_types"][venue_type] = profile["preferences"]["venue_types"].get(venue_type, 0) + 1
        
        # 学习场景偏好
        scenario = context.get("meeting_scenario", "其他")
        profile["preferences"]["scenarios"][scenario] = profile["preferences"]["scenarios"].get(scenario, 0) + 1
        
        # 学习特征偏好
        place_tags = selected_place.get("tag", "").split(";")
        for tag in place_tags:
            if tag.strip():
                profile["preferences"]["features"][tag.strip()] = profile["preferences"]["features"].get(tag.strip(), 0) + 1
        
        # 学习价格敏感度
        rating = float(selected_place.get("biz_ext", {}).get("rating", "0") or "0")
        if rating > 0:
            price_category = "高档" if rating >= 4.5 else "中档" if rating >= 3.5 else "经济"
            profile["preferences"]["price_range"][price_category] = profile["preferences"]["price_range"].get(price_category, 0) + 1
        
        # 学习距离容忍度
        distance = selected_place.get("_distance_from_center", 0)
        if distance > 0:
            profile["preferences"]["distance_tolerance"].append(distance)
            # 保持最近20次的距离记录
            if len(profile["preferences"]["distance_tolerance"]) > 20:
                profile["preferences"]["distance_tolerance"] = profile["preferences"]["distance_tolerance"][-20:]
        
        logger.info(f"学习用户偏好: {user_id}, 交互次数: {profile['interaction_count']}")
    
    def generate_personalized_weights(self, user_id: str, context: Dict) -> Dict[str, float]:
        """为用户生成个性化权重"""
        if user_id not in self.user_profiles:
            return self._get_default_weights()
        
        profile = self.user_profiles[user_id]
        
        # 如果交互次数太少，使用默认权重
        if profile["interaction_count"] < 3:
            return self._get_default_weights()
        
        weights = self._get_default_weights()
        preferences = profile["preferences"]
        
        # 基于场所类型偏好调整权重
        current_venue_type = context.get("keywords", "")
        if current_venue_type in preferences["venue_types"]:
            # 用户对这种场所类型有偏好，提高相关权重
            weights["venue_match"] *= 1.3
        
        # 基于场景偏好调整权重
        current_scenario = context.get("meeting_scenario", "")
        if current_scenario in preferences["scenarios"]:
            weights["scenario_match"] *= 1.2
        
        # 基于价格偏好调整权重
        preferred_price = max(preferences["price_range"], key=preferences["price_range"].get) if preferences["price_range"] else None
        if preferred_price:
            if preferred_price == "高档":
                weights["rating"] *= 1.4
            elif preferred_price == "经济":
                weights["distance"] *= 1.2
        
        # 基于距离容忍度调整权重
        if preferences["distance_tolerance"]:
            avg_distance = sum(preferences["distance_tolerance"]) / len(preferences["distance_tolerance"])
            if avg_distance < 1000:  # 用户偏好近距离
                weights["distance"] *= 1.5
            elif avg_distance > 3000:  # 用户容忍远距离
                weights["distance"] *= 0.8
                weights["rating"] *= 1.2
        
        logger.info(f"为用户 {user_id} 生成个性化权重: {weights}")
        return weights
    
    def _get_default_weights(self) -> Dict[str, float]:
        """获取默认权重"""
        return {
            "distance": 1.0,
            "rating": 1.0,
            "venue_match": 1.0,
            "scenario_match": 1.0,
            "business_status": 1.0,
            "weather_suitability": 1.0,
            "traffic_condition": 1.0,
            "group_size_fit": 1.0
        }
    
    def get_personalized_recommendations(self, user_id: str, places: List[Dict], context: Dict) -> List[str]:
        """获取个性化推荐理由"""
        if user_id not in self.user_profiles:
            return []
        
        profile = self.user_profiles[user_id]
        preferences = profile["preferences"]
        recommendations = []
        
        # 基于历史偏好生成推荐理由
        if preferences["venue_types"]:
            most_preferred_venue = max(preferences["venue_types"], key=preferences["venue_types"].get)
            current_venue = context.get("keywords", "")
            if most_preferred_venue == current_venue:
                recommendations.append(f"根据您的偏好，您经常选择{most_preferred_venue}")
        
        if preferences["scenarios"]:
            most_preferred_scenario = max(preferences["scenarios"], key=preferences["scenarios"].get)
            current_scenario = context.get("meeting_scenario", "")
            if most_preferred_scenario == current_scenario:
                recommendations.append(f"您在{most_preferred_scenario}场景下有很好的体验")
        
        if preferences["features"]:
            top_features = sorted(preferences["features"].items(), key=lambda x: x[1], reverse=True)[:3]
            feature_names = [f[0] for f in top_features]
            recommendations.append(f"推荐具有以下特色的场所: {', '.join(feature_names)}")
        
        if preferences["distance_tolerance"]:
            avg_distance = sum(preferences["distance_tolerance"]) / len(preferences["distance_tolerance"])
            if avg_distance < 1000:
                recommendations.append("为您优先推荐步行可达的近距离场所")
            elif avg_distance > 3000:
                recommendations.append("为您推荐评分更高的场所，距离稍远但值得一去")
        
        return recommendations
    
    def analyze_user_behavior(self, user_id: str) -> Dict[str, Any]:
        """分析用户行为模式"""
        if user_id not in self.user_profiles:
            return {"status": "new_user", "analysis": "用户数据不足，正在学习中..."}
        
        profile = self.user_profiles[user_id]
        preferences = profile["preferences"]
        
        analysis = {
            "status": "analyzing",
            "interaction_count": profile["interaction_count"],
            "user_type": self._determine_user_type(preferences),
            "preferred_venues": list(preferences["venue_types"].keys()),
            "preferred_scenarios": list(preferences["scenarios"].keys()),
            "behavior_summary": self._generate_behavior_summary(preferences)
        }
        
        return analysis
    
    def _determine_user_type(self, preferences: Dict) -> str:
        """确定用户类型"""
        venue_types = preferences.get("venue_types", {})
        scenarios = preferences.get("scenarios", {})
        price_range = preferences.get("price_range", {})
        
        # 基于偏好模式分类用户
        if "咖啡馆" in venue_types and venue_types["咖啡馆"] > 2:
            if "商务会议" in scenarios:
                return "商务精英型"
            else:
                return "咖啡爱好者"
        elif "餐厅" in venue_types and venue_types["餐厅"] > 2:
            return "美食家"
        elif "运动健身" in scenarios:
            return "运动达人"
        elif "高档" in price_range and price_range["高档"] > 1:
            return "品质追求者"
        elif "经济" in price_range and price_range["经济"] > 2:
            return "实用主义者"
        else:
            return "探索者"
    
    def _generate_behavior_summary(self, preferences: Dict) -> List[str]:
        """生成行为总结"""
        summary = []
        
        # 场所偏好分析
        venue_types = preferences.get("venue_types", {})
        if venue_types:
            most_preferred = max(venue_types, key=venue_types.get)
            summary.append(f"最喜欢的场所类型: {most_preferred}")
        
        # 场景偏好分析
        scenarios = preferences.get("scenarios", {})
        if scenarios:
            most_preferred_scenario = max(scenarios, key=scenarios.get)
            summary.append(f"最常见的会面场景: {most_preferred_scenario}")
        
        # 距离偏好分析
        distances = preferences.get("distance_tolerance", [])
        if distances:
            avg_distance = sum(distances) / len(distances)
            if avg_distance < 1000:
                summary.append("偏好就近选择，注重便利性")
            elif avg_distance > 3000:
                summary.append("愿意为了更好的体验接受较远距离")
            else:
                summary.append("在距离和体验之间保持平衡")
        
        # 价格敏感度分析
        price_range = preferences.get("price_range", {})
        if price_range:
            if "高档" in price_range and price_range["高档"] > 1:
                summary.append("注重服务品质，对价格不敏感")
            elif "经济" in price_range and price_range["经济"] > 2:
                summary.append("注重性价比，偏好经济实惠的选择")
        
        return summary
    
    def get_smart_suggestions(self, user_id: str, context: Dict) -> List[str]:
        """基于用户画像提供智能建议"""
        suggestions = []
        
        if user_id not in self.user_profiles:
            suggestions.extend([
                "尝试不同类型的场所来发现您的偏好",
                "可以在特殊要求中注明您的具体需求",
                "系统正在学习您的偏好，请多次使用以获得更精准的推荐"
            ])
            return suggestions
        
        profile = self.user_profiles[user_id]
        user_type = self._determine_user_type(profile["preferences"])
        current_scenario = context.get("meeting_scenario", "")
        group_size = context.get("group_size", 2)
        
        # 基于用户类型的建议
        if user_type == "商务精英型":
            suggestions.extend([
                "建议选择有包间或安静环境的场所",
                "推荐具有WiFi和商务设施的地点",
                "考虑交通便利和停车方便的位置"
            ])
        elif user_type == "咖啡爱好者":
            suggestions.extend([
                "推荐尝试不同品牌的咖啡馆",
                "可以关注有特色咖啡或手冲咖啡的店铺",
                "建议选择氛围良好适合聊天的环境"
            ])
        elif user_type == "美食家":
            suggestions.extend([
                "建议尝试不同菜系的餐厅",
                "可以关注评分较高或有特色菜品的餐厅",
                "考虑提前预订，特别是热门餐厅"
            ])
        elif user_type == "运动达人":
            suggestions.extend([
                "建议选择有更衣室和淋浴设施的场所",
                "可以考虑运动后的放松和补充场所",
                "注意运动场所的器材质量和环境"
            ])
        
        # 基于群组大小的建议
        if group_size > 6:
            suggestions.append("大团体建议选择有包间或大桌的场所")
        elif group_size == 2:
            suggestions.append("两人会面可以选择更私密的环境")
        
        # 基于场景的建议
        if current_scenario == "商务会议":
            suggestions.append("商务会议建议选择安静、正式的环境")
        elif current_scenario == "约会":
            suggestions.append("约会场景建议选择浪漫、私密的环境")
        
        return suggestions[:5]  # 限制建议数量


# 创建全局实例
preference_engine = PreferenceEngine()