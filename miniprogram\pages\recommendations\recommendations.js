// recommendations.js
Page({
  data: {
    centerLocation: {
      latitude: 39.960799,
      longitude: 116.4755145
    },
    locations: [],
    keywords: '',
    userRequirements: '',
    places: [],
    markers: []
  },
  
  onLoad: function(options) {
    if (options.data) {
      try {
        const data = JSON.parse(decodeURIComponent(options.data));
        
        // 设置中心点
        const centerLocation = data.center || this.data.centerLocation;
        
        // 构建标记点数据
        const markers = [];
        
        // 添加中心点标记
        markers.push({
          id: 0,
          latitude: centerLocation.latitude,
          longitude: centerLocation.longitude,
          title: '最佳会面点',
          iconPath: '/images/center.png',
          width: 30,
          height: 30
        });
        
        // 添加位置标记
        if (data.locations && data.locations.length) {
          // 这里应该通过地理编码API获取位置的经纬度
          // 这里使用模拟数据
          const locationCoords = [
            { latitude: 39.921243, longitude: 116.469759 },
            { latitude: 40.000355, longitude: 116.48127 }
          ];
          
          data.locations.forEach((location, index) => {
            if (index < locationCoords.length) {
              markers.push({
                id: index + 1,
                latitude: locationCoords[index].latitude,
                longitude: locationCoords[index].longitude,
                title: location,
                iconPath: '/images/location.png',
                width: 30,
                height: 30
              });
            }
          });
        }
        
        // 添加推荐地点标记
        if (data.places && data.places.length) {
          // 这里应该通过地理编码API获取地点的经纬度
          // 这里使用模拟数据
          const placeCoords = [
            { latitude: 39.963775, longitude: 116.470505 },
            { latitude: 39.962963, longitude: 116.470413 }
          ];
          
          data.places.forEach((place, index) => {
            if (index < placeCoords.length) {
              markers.push({
                id: index + data.locations.length + 1,
                latitude: placeCoords[index].latitude,
                longitude: placeCoords[index].longitude,
                title: place.name,
                iconPath: '/images/place.png',
                width: 30,
                height: 30
              });
            }
          });
        }
        
        this.setData({
          centerLocation: centerLocation,
          locations: data.locations || [],
          keywords: data.keywords || '',
          userRequirements: data.userRequirements || '',
          places: data.places || [],
          markers: markers
        });
      } catch (error) {
        wx.showToast({
          title: '数据解析错误',
          icon: 'none'
        });
        console.error('数据解析错误:', error);
      }
    }
  },
  
  // 打开位置
  openLocation: function(e) {
    const { index } = e.currentTarget.dataset;
    const place = this.data.places[index];
    
    // 查找对应的标记点
    const marker = this.data.markers.find(m => m.title === place.name);
    
    if (marker) {
      wx.openLocation({
        latitude: marker.latitude,
        longitude: marker.longitude,
        name: place.name,
        address: place.address,
        scale: 18
      });
    } else {
      wx.showToast({
        title: '无法获取位置信息',
        icon: 'none'
      });
    }
  },
  
  // 拨打电话
  makePhoneCall: function(e) {
    const { phone } = e.currentTarget.dataset;
    
    if (phone) {
      wx.makePhoneCall({
        phoneNumber: phone
      });
    }
  }
}); 