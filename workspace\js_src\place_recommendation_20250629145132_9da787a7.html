<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健身汇 - 最佳会面健身房推荐</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/boxicons@2.0.9/css/boxicons.min.css">
    <style>
        
        :root {
            --primary: #e67e22; 
            --primary-light: #f39c12;
            --primary-dark: #d35400;
            --secondary: #fdebd0;
            --light: #fef9e7;
            --dark: #4a2c03;
            --success: #4a934a; /* Success color can remain static or be themed */
            --border-radius: 12px;
            --box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            --transition: all 0.3s ease;
        } /* Inject dynamic theme colors here */

        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; line-height: 1.6; background-color: var(--light); color: var(--dark); padding-bottom: 50px; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        header { background-color: var(--primary); color: white; padding: 60px 0 100px; text-align: center; position: relative; margin-bottom: 80px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); }
        header::after { content: ''; position: absolute; bottom: 0; left: 0; right: 0; height: 60px; background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 60"><path fill="#fef9e7" fill-opacity="1" d="M0,32L80,42.7C160,53,320,75,480,64C640,53,800,11,960,5.3C1120,0,1280,32,1360,48L1440,64L1440,100L1360,100C1280,100,1120,100,960,100C800,100,640,100,480,100C320,100,160,100,80,100L0,100Z"></path></svg>'); background-size: cover; background-position: center; }
        .header-logo { font-size: 3rem; font-weight: 700; margin-bottom: 10px; letter-spacing: -1px; }
        .coffee-icon { font-size: 3rem; vertical-align: middle; margin-right: 10px; }
        .header-subtitle { font-size: 1.2rem; opacity: 0.9; }
        .main-content { margin-top: -60px; }
        .card { background-color: white; border-radius: var(--border-radius); padding: 30px; box-shadow: var(--box-shadow); margin-bottom: 30px; transition: var(--transition); }
        .card:hover { transform: translateY(-5px); box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1); }
        .section-title { font-size: 1.8rem; color: var(--primary-dark); margin-bottom: 25px; padding-bottom: 15px; border-bottom: 2px solid var(--secondary); display: flex; align-items: center; }
        .section-title i { margin-right: 12px; font-size: 1.6rem; color: var(--primary); }
        .summary-card { display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 15px; }
        .summary-item { flex: 1; min-width: 200px; padding: 15px; background-color: rgba(0,0,0,0.03); /* Adjusted for better contrast with various themes */ border-radius: 8px; border-left: 4px solid var(--primary); }
        .summary-label { font-size: 0.9rem; color: var(--primary-dark); margin-bottom: 5px; }
        .summary-value { font-size: 1.2rem; font-weight: 600; color: var(--dark); }
        .map-container { height: 500px; border-radius: var(--border-radius); overflow: hidden; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); position: relative; margin-bottom: 30px; }
        #map { height: 100%; width: 100%; }
        .map-legend { position: absolute; bottom: 15px; left: 15px; background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.15); z-index: 100; }
        .legend-item { display: flex; align-items: center; margin-bottom: 8px; }
        .legend-color { width: 20px; height: 20px; margin-right: 10px; border-radius: 50%; }
        .legend-center { background-color: #2ecc71; } 
        .legend-location { background-color: #3498db; } 
        .legend-place { background-color: #e74c3c; } 
        .location-table { width: 100%; border-collapse: collapse; border-radius: 8px; overflow: hidden; margin-bottom: 25px; box-shadow: 0 0 8px rgba(0, 0, 0, 0.1); }
        .location-table th, .location-table td { padding: 15px; text-align: left; border-bottom: 1px solid #eee; }
        .location-table th { background-color: var(--primary-light); color: white; font-weight: 600; }
        .location-table tr:last-child td { border-bottom: none; }
        .location-table tr:nth-child(even) { background-color: rgba(0,0,0,0.02); /* Adjusted for better contrast */ }
        .cafe-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 25px; margin-top: 20px; } 
        .cafe-card { background-color: white; border-radius: 12px; overflow: hidden; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08); transition: var(--transition); display: flex; flex-direction: column; }
        .cafe-card:hover { transform: translateY(-10px); box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15); }
        .cafe-img { height: 180px; background-color: var(--primary-light); display: flex; align-items: center; justify-content: center; color: white; font-size: 3rem; }
        .cafe-content { padding: 20px; flex: 1; display: flex; flex-direction: column; }
        .cafe-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 15px; }
        .cafe-name { font-size: 1.3rem; margin: 0 0 5px 0; color: var(--primary-dark); }
        .cafe-rating { display: inline-block; background-color: var(--primary); color: white; padding: 5px 12px; border-radius: 20px; font-weight: 600; font-size: 0.9rem; white-space: nowrap; }
        .cafe-details { flex: 1; }
        .cafe-info { margin-bottom: 12px; display: flex; align-items: flex-start; }
        .cafe-info i { color: var(--primary); margin-right: 8px; font-size: 1.1rem; min-width: 20px; margin-top: 3px; }
        .cafe-info-text { flex: 1; }
        .cafe-tags { display: flex; flex-wrap: wrap; gap: 6px; margin-top: 15px; }
        .cafe-tag { background-color: rgba(0,0,0,0.05); /* Adjusted for better contrast */ color: var(--primary-dark); padding: 4px 10px; border-radius: 15px; font-size: 0.8rem; }
        .cafe-footer { display: flex; align-items: center; justify-content: space-between; margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee; }
        .cafe-distance { display: flex; align-items: center; color: var(--primary-dark); font-weight: 600; }
        .cafe-distance i { margin-right: 5px; }
        .cafe-actions a { display: inline-flex; align-items: center; justify-content: center; background-color: var(--primary); color: white; padding: 8px 15px; border-radius: 6px; text-decoration: none; font-size: 0.9rem; transition: var(--transition); }
        .cafe-actions a:hover { background-color: var(--primary-dark); transform: translateY(-2px); }
        .cafe-actions i { margin-right: 5px; }
        .transportation-info { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px; margin-top: 20px; }
        .transport-card { background-color: white; border-radius: 12px; padding: 25px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); border-top: 5px solid var(--primary); }
        .transport-title { font-size: 1.3rem; color: var(--primary-dark); margin-bottom: 15px; display: flex; align-items: center; }
        .transport-title i { margin-right: 10px; font-size: 1.4rem; color: var(--primary); }
        .transport-list { list-style: none; margin: 0; padding: 0; }
        .transport-list li { padding: 10px 0; border-bottom: 1px solid #eee; display: flex; align-items: center; }
        .transport-list li:last-child { border-bottom: none; }
        .transport-list i { color: var(--primary); margin-right: 10px; }
        .center-coords { display: inline-block; background-color: rgba(0,0,0,0.05); /* Adjusted for better contrast */ border-radius: 6px; padding: 3px 8px; margin: 0 5px; font-family: monospace; font-size: 0.9rem; }
        .footer { text-align: center; background-color: var(--primary-dark); color: white; padding: 20px 0; margin-top: 50px; }
        .back-button { display: inline-flex; align-items: center; justify-content: center; background-color: white; color: var(--primary); border: 2px solid var(--primary); padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; font-size: 1rem; transition: var(--transition); margin-top: 30px; }
        .back-button:hover { background-color: var(--primary); color: white; transform: translateY(-3px); box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); }
        .back-button i { margin-right: 8px; }
        .search-process-card { position: relative; overflow: hidden; background-color: #fafafa; border-left: 5px solid #2c3e50; } /* Search process card can have static border */
        .search-process { position: relative; padding: 20px 0; }
        .process-step { display: flex; margin-bottom: 30px; opacity: 0.5; transform: translateX(-20px); transition: opacity 0.5s ease, transform 0.5s ease; }
        .process-step.active { opacity: 1; transform: translateX(0); }
        .step-icon { flex: 0 0 60px; height: 60px; border-radius: 50%; background-color: var(--primary-light); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem; margin-right: 20px; position: relative; }
        .step-number { position: absolute; top: -5px; right: -5px; width: 25px; height: 25px; border-radius: 50%; background-color: var(--primary-dark); color: white; display: flex; align-items: center; justify-content: center; font-size: 0.8rem; font-weight: bold; }
        .step-content { flex: 1; }
        .step-title { font-size: 1.3rem; color: var(--primary-dark); margin-bottom: 10px; }
        .step-details { background-color: white; border-radius: 10px; padding: 15px; box-shadow: 0 3px 10px rgba(0,0,0,0.05); }
        .code-block { background-color: #2c3e50; color: #e6e6e6; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 0.9rem; margin: 15px 0; white-space: pre; overflow-x: auto; }
        .highlight-text { background-color: rgba(46, 204, 113, 0.2); color: #2c3e50; padding: 3px 6px; border-radius: 4px; font-weight: bold; }
        .search-animation { height: 200px; position: relative; display: flex; align-items: center; justify-content: center; margin: 20px 0; }
        .radar-circle { position: absolute; width: 50px; height: 50px; border-radius: 50%; background-color: rgba(52, 152, 219, 0.1); animation: radar 3s infinite; }
        .radar-circle:nth-child(1) { animation-delay: 0s; } .radar-circle:nth-child(2) { animation-delay: 1s; } .radar-circle:nth-child(3) { animation-delay: 2s; }
        .center-point { width: 15px; height: 15px; border-radius: 50%; background-color: #e74c3c; z-index: 2; box-shadow: 0 0 0 5px rgba(231, 76, 60, 0.3); }
        .map-operation-animation { height: 200px; position: relative; border-radius: 8px; overflow: hidden; background-color: #f5f5f5; margin: 20px 0; box-shadow: 0 3px 10px rgba(0,0,0,0.1); }
        .map-bg { position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23f0f0f0"/><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="%23ccc" stroke-width="0.5"/><path d="M50,0 L50,100 M0,50 L100,50" stroke="%23ccc" stroke-width="0.5"/></svg>'); background-size: 50px 50px; opacity: 0.7; }
        .map-cursor { position: absolute; width: 20px; height: 20px; background-color: rgba(231, 76, 60, 0.7); border-radius: 50%; top: 50%; left: 30%; transform: translate(-50%, -50%); animation: mapCursor 4s infinite ease-in-out; z-index: 2; }
        .map-search-indicator { position: absolute; width: 80px; height: 80px; border: 2px dashed rgba(52, 152, 219, 0.6); border-radius: 50%; top: 50%; left: 50%; transform: translate(-50%, -50%); animation: mapSearch 3s infinite ease-in-out; z-index: 1; }
        @keyframes mapCursor { 0% { left: 30%; top: 30%; } 30% { left: 60%; top: 40%; } 60% { left: 40%; top: 70%; } 100% { left: 30%; top: 30%; } }
        @keyframes mapSearch { 0% { width: 30px; height: 30px; opacity: 1; } 100% { width: 150px; height: 150px; opacity: 0; } }
        @keyframes radar { 0% { width: 40px; height: 40px; opacity: 1; } 100% { width: 300px; height: 300px; opacity: 0; } }
        .ranking-result { margin-top: 15px; }
        .result-bar { height: 30px; background-color: var(--primary); color: white; margin-bottom: 8px; border-radius: 15px; padding: 0 15px; display: flex; align-items: center; font-weight: 600; box-shadow: 0 2px 5px rgba(0,0,0,0.1); animation: growBar 2s ease; transform-origin: left; }
        @keyframes growBar { 0% { width: 0; } 100% { width: 100%; } }
        .mt-4 { margin-top: 1rem; }
        @media (max-width: 768px) { .cafe-grid { grid-template-columns: 1fr; } .transportation-info { grid-template-columns: 1fr; } header { padding: 40px 0 80px; } .header-logo { font-size: 2.2rem; } .process-step { flex-direction: column; } .step-icon { margin-bottom: 15px; margin-right: 0; } }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-logo">
                <i class='bx bx-dumbbell coffee-icon'></i>健身汇
            </div>
            <div class="header-subtitle">为您找到的最佳会面健身房</div>
        </div>
    </header>

    <div class="container main-content">
        <div class="card">
            <h2 class="section-title"><i class='bx bx-info-circle'></i>推荐摘要</h2>
            <div class="summary-card">
                <div class="summary-item">
                    <div class="summary-label">参与地点数</div>
                    <div class="summary-value">2 个地点</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">推荐健身房数</div>
                    <div class="summary-value">5 家健身房</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">特殊需求</div>
                    <div class="summary-value">无特殊需求</div>
                </div>
            </div>
        </div>
        
        <div class="card search-process-card">
            <h2 class="section-title"><i class='bx bx-bot'></i>AI 搜索过程</h2>
            <div class="search-process">
            <div class="process-step" data-step="1">
                <div class="step-icon"><i class='bx bx-map-pin'></i><div class="step-number">1</div></div>
                <div class="step-content"><h3 class="step-title">分析用户位置信息</h3><div class="step-details"><p>我检测到2个不同的位置。正在分析它们的地理分布...</p><ul><li>分析位置 1: <strong>北京大学</strong></li><li>分析位置 2: <strong>清华大学</strong></li></ul></div></div>
            </div>
            <div class="process-step" data-step="2">
                <div class="step-icon"><i class='bx bx-map'></i><div class="step-number">2</div></div>
                <div class="step-content"><h3 class="step-title">正在操作高德地图寻找最佳健身房的位置...</h3><div class="step-details">
            <p>正在操作高德地图寻找最佳健身房的位置...</p> 
            <div class="map-operation-animation">
                <div class="map-bg"></div> <div class="map-cursor"></div> <div class="map-search-indicator"></div>
            </div></div></div>
            </div>
            <div class="process-step" data-step="3">
                <div class="step-icon"><i class='bx bx-list-check'></i><div class="step-number">3</div></div>
                <div class="step-content"><h3 class="step-title">分析用户特殊需求</h3><div class="step-details"><p>未提供特殊需求，将根据评分和位置便利性进行推荐健身房。</p></div></div>
            </div>
            <div class="process-step" data-step="4">
                <div class="step-icon"><i class='bx bx-search-alt'></i><div class="step-number">4</div></div>
                <div class="step-content"><h3 class="step-title">搜索周边健身房</h3><div class="step-details">
        <p>我正在以最佳会面点为中心，搜索周边2公里范围内的健身房...</p> 
        <div class="search-animation">
            <div class="radar-circle"></div> <div class="radar-circle"></div> <div class="radar-circle"></div>
            <div class="center-point"></div>
        </div></div></div>
            </div>
            <div class="process-step" data-step="5">
                <div class="step-icon"><i class='bx bx-sort'></i><div class="step-number">5</div></div>
                <div class="step-content"><h3 class="step-title">对健身房进行排名</h3><div class="step-details">
        <p>我已找到多家健身房，正在根据综合评分对它们进行排名...</p> 
        <div class="ranking-result">
            <div class="result-bar" style="width: 95%;">健身房评分</div> 
            <div class="result-bar" style="width: 85%;">距离便利性</div>
            <div class="result-bar" style="width: 75%;">环境舒适度</div>
            <div class="result-bar" style="width: 65%;">交通便利性</div>
        </div></div></div>
            </div></div>
            
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            const steps = document.querySelectorAll('.process-step');
            let currentStep = 0;
            function showNextStep() {
                if (currentStep < steps.length) {
                    steps[currentStep].classList.add('active');
                    currentStep++;
                    setTimeout(showNextStep, 1500); 
                }
            }
            setTimeout(showNextStep, 500); 
        });
        </script>
        </div>
        <div class="card">
            <h2 class="section-title"><i class='bx bx-map-pin'></i>地点信息</h2>
            <table class="location-table">
                <thead><tr><th>序号</th><th>地点名称</th><th>详细地址</th></tr></thead>
                <tbody><tr><td>1</td><td>北京大学</td><td>北京市海淀区北京大学</td></tr><tr><td>2</td><td>清华大学</td><td>北京市海淀区清华大学</td></tr></tbody>
            </table>
        </div>
        <div class="card">
            <h2 class="section-title"><i class='bx bx-map-alt'></i>地图展示</h2>
            <div class="map-container">
                <div id="map"></div>
                <div class="map-legend">
                    <div class="legend-item"><div class="legend-color legend-center"></div><span>最佳会面点</span></div>
                    <div class="legend-item"><div class="legend-color legend-location"></div><span>所在地点</span></div>
                    <div class="legend-item"><div class="legend-color legend-place"></div><span>健身房</span></div>
                </div>
            </div>
        </div>
        <div class="card">
            <h2 class="section-title"><i class='bx bx-dumbbell'></i>推荐健身房</h2>
            <div class="cafe-grid">
                
            <div class="cafe-card"> 
                <div class="cafe-img">
                    <i class='bx bx-dumbbell'></i> 
                </div>
                <div class="cafe-content">
                    <div class="cafe-header">
                        <div>
                            <h3 class="cafe-name">北京大学体教部(理教)健身中心</h3>
                        </div>
                        <span class="cafe-rating">评分: 4.3</span>
                    </div>
                    <div class="cafe-details">
                        <div class="cafe-info">
                            <i class='bx bx-map'></i>
                            <div class="cafe-info-text">颐和园路5号北京大学理科1号楼</div>
                        </div>
                        <div class="cafe-info">
                            <i class='bx bx-time'></i>
                            <div class="cafe-info-text">营业时间未知</div>
                        </div>
                        <div class="cafe-info">
                            <i class='bx bx-phone'></i>
                            <div class="cafe-info-text">[]</div>
                        </div>
                        <div class="cafe-tags">
                            <span class='cafe-tag'>健身房</span>
                        </div>
                    </div>
                    <div class="cafe-footer">
                        <div class="cafe-distance">
                            <i class='bx bx-walk'></i> 0.9 公里
                        </div>
                        <div class="cafe-actions">
                            <a href="https://uri.amap.com/marker?position=116.313032,39.990875&name=北京大学体教部(理教)健身中心" target="_blank">
                                <i class='bx bx-navigation'></i>导航
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="cafe-card"> 
                <div class="cafe-img">
                    <i class='bx bx-dumbbell'></i> 
                </div>
                <div class="cafe-content">
                    <div class="cafe-header">
                        <div>
                            <h3 class="cafe-name">B Active必爱体24小时健身(五道口店)</h3>
                        </div>
                        <span class="cafe-rating">评分: 4.4</span>
                    </div>
                    <div class="cafe-details">
                        <div class="cafe-info">
                            <i class='bx bx-map'></i>
                            <div class="cafe-info-text">中关村东路1号院启迪科技大厦C座B1层</div>
                        </div>
                        <div class="cafe-info">
                            <i class='bx bx-time'></i>
                            <div class="cafe-info-text">营业时间未知</div>
                        </div>
                        <div class="cafe-info">
                            <i class='bx bx-phone'></i>
                            <div class="cafe-info-text">[]</div>
                        </div>
                        <div class="cafe-tags">
                            <span class='cafe-tag'>健身房</span>
                        </div>
                    </div>
                    <div class="cafe-footer">
                        <div class="cafe-distance">
                            <i class='bx bx-walk'></i> 1.1 公里
                        </div>
                        <div class="cafe-actions">
                            <a href="https://uri.amap.com/marker?position=116.330376,39.993268&name=B Active必爱体24小时健身(五道口店)" target="_blank">
                                <i class='bx bx-navigation'></i>导航
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="cafe-card"> 
                <div class="cafe-img">
                    <i class='bx bx-dumbbell'></i> 
                </div>
                <div class="cafe-content">
                    <div class="cafe-header">
                        <div>
                            <h3 class="cafe-name">爱尚游泳健身(唐宁ONE店)</h3>
                        </div>
                        <span class="cafe-rating">评分: 4.7</span>
                    </div>
                    <div class="cafe-details">
                        <div class="cafe-info">
                            <i class='bx bx-map'></i>
                            <div class="cafe-info-text">中关村东路16号院1号楼</div>
                        </div>
                        <div class="cafe-info">
                            <i class='bx bx-time'></i>
                            <div class="cafe-info-text">营业时间未知</div>
                        </div>
                        <div class="cafe-info">
                            <i class='bx bx-phone'></i>
                            <div class="cafe-info-text">010-82194860</div>
                        </div>
                        <div class="cafe-tags">
                            <span class='cafe-tag'>健身房</span>
                        </div>
                    </div>
                    <div class="cafe-footer">
                        <div class="cafe-distance">
                            <i class='bx bx-walk'></i> 1.6 公里
                        </div>
                        <div class="cafe-actions">
                            <a href="https://uri.amap.com/marker?position=116.333571,39.989613&name=爱尚游泳健身(唐宁ONE店)" target="_blank">
                                <i class='bx bx-navigation'></i>导航
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="cafe-card"> 
                <div class="cafe-img">
                    <i class='bx bx-dumbbell'></i> 
                </div>
                <div class="cafe-content">
                    <div class="cafe-header">
                        <div>
                            <h3 class="cafe-name">BA健身(搜狐店)</h3>
                        </div>
                        <span class="cafe-rating">评分: 4.3</span>
                    </div>
                    <div class="cafe-details">
                        <div class="cafe-info">
                            <i class='bx bx-map'></i>
                            <div class="cafe-info-text">搜狐网络大厦星巴克旁</div>
                        </div>
                        <div class="cafe-info">
                            <i class='bx bx-time'></i>
                            <div class="cafe-info-text">营业时间未知</div>
                        </div>
                        <div class="cafe-info">
                            <i class='bx bx-phone'></i>
                            <div class="cafe-info-text">010-82150607</div>
                        </div>
                        <div class="cafe-tags">
                            <span class='cafe-tag'>健身房</span>
                        </div>
                    </div>
                    <div class="cafe-footer">
                        <div class="cafe-distance">
                            <i class='bx bx-walk'></i> 1.2 公里
                        </div>
                        <div class="cafe-actions">
                            <a href="https://uri.amap.com/marker?position=116.332156,39.993453&name=BA健身(搜狐店)" target="_blank">
                                <i class='bx bx-navigation'></i>导航
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="cafe-card"> 
                <div class="cafe-img">
                    <i class='bx bx-dumbbell'></i> 
                </div>
                <div class="cafe-content">
                    <div class="cafe-header">
                        <div>
                            <h3 class="cafe-name">OOHLiVE运动空间</h3>
                        </div>
                        <span class="cafe-rating">评分: 4.2</span>
                    </div>
                    <div class="cafe-details">
                        <div class="cafe-info">
                            <i class='bx bx-map'></i>
                            <div class="cafe-info-text">中关村东路1号院8号楼科技大厦D座一层DG03</div>
                        </div>
                        <div class="cafe-info">
                            <i class='bx bx-time'></i>
                            <div class="cafe-info-text">营业时间未知</div>
                        </div>
                        <div class="cafe-info">
                            <i class='bx bx-phone'></i>
                            <div class="cafe-info-text">13161702567</div>
                        </div>
                        <div class="cafe-tags">
                            <span class='cafe-tag'>健身房</span>
                        </div>
                    </div>
                    <div class="cafe-footer">
                        <div class="cafe-distance">
                            <i class='bx bx-walk'></i> 1.2 公里
                        </div>
                        <div class="cafe-actions">
                            <a href="https://uri.amap.com/marker?position=116.331068,39.993179&name=OOHLiVE运动空间" target="_blank">
                                <i class='bx bx-navigation'></i>导航
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
        <div class="card">
            <h2 class="section-title"><i class='bx bx-car'></i>交通与停车建议</h2>
            <div class="transportation-info">
                <div class="transport-card">
                    <h3 class="transport-title"><i class='bx bx-trip'></i>前往方式</h3>
                    <p>最佳会面点位于<span class="center-coords">116.318927, 39.998043</span>附近</p>
                    <ul class="transport-list"><li><i class='bx bx-map'></i><strong>北京大学</strong>: 距离中心点约 <span class='distance'>0.9 公里</span></li><li><i class='bx bx-map'></i><strong>清华大学</strong>: 距离中心点约 <span class='distance'>0.9 公里</span></li></ul>
                </div>
                <div class="transport-card">
                    <h3 class="transport-title"><i class='bx bxs-car-garage'></i>停车建议</h3>
                    <ul class="transport-list">
                        <li><i class='bx bx-check'></i>大部分推荐的健身房周边有停车场或提供停车服务</li>
                        <li><i class='bx bx-check'></i>建议使用高德地图或百度地图导航到目的地</li>
                        <li><i class='bx bx-check'></i>高峰时段建议提前30分钟出发，以便寻找停车位</li>
                        <li><i class='bx bx-check'></i>部分健身房可能提供免费停车或停车优惠</li>
                    </ul>
                </div>
            </div>
            <a href="/workspace/meetspot_finder.html" class="back-button"> 
                <i class='bx bx-left-arrow-alt'></i>返回首页
            </a>
        </div>
    </div>
    <footer class="footer">
        <div class="container">
            <p>© 2025 健身汇 - 智能健身房推荐服务 | 数据来源：高德地图</p>
        </div>
    </footer>
    <script type="text/javascript">
        var markersData = [{"name": "\u6700\u4f73\u4f1a\u9762\u70b9", "position": [116.318927, 39.998043], "icon": "center"}, {"name": "\u5730\u70b91: \u5317\u4eac\u5927\u5b66", "position": [116.310918, 39.992873], "icon": "location"}, {"name": "\u5730\u70b92: \u6e05\u534e\u5927\u5b66", "position": [116.326936, 40.003213], "icon": "location"}, {"name": "\u5317\u4eac\u5927\u5b66\u4f53\u6559\u90e8(\u7406\u6559)\u5065\u8eab\u4e2d\u5fc3", "position": [116.313032, 39.990875], "icon": "place"}, {"name": "B Active\u5fc5\u7231\u4f5324\u5c0f\u65f6\u5065\u8eab(\u4e94\u9053\u53e3\u5e97)", "position": [116.330376, 39.993268], "icon": "place"}, {"name": "\u7231\u5c1a\u6e38\u6cf3\u5065\u8eab(\u5510\u5b81ONE\u5e97)", "position": [116.333571, 39.989613], "icon": "place"}, {"name": "BA\u5065\u8eab(\u641c\u72d0\u5e97)", "position": [116.332156, 39.993453], "icon": "place"}, {"name": "OOHLiVE\u8fd0\u52a8\u7a7a\u95f4", "position": [116.331068, 39.993179], "icon": "place"}];
        window._AMapSecurityConfig = { securityJsCode: "" };
        window.onload = function() {
            var script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = 'https://webapi.amap.com/loader.js';
            script.onload = function() {
                AMapLoader.load({
                    key: "041db813f69a2424f234fade1e3b3605", 
                    version: "2.0",
                    plugins: ["AMap.Scale", "AMap.ToolBar"],
                    AMapUI: { version: "1.1", plugins: ["overlay/SimpleMarker"] }
                })
                .then(function(AMap) { initMap(AMap); })
                .catch(function(e) { console.error('地图加载失败:', e); });
            };
            document.body.appendChild(script);
            animateCafeCards(); 
        };
        function initMap(AMap) {
            var map = new AMap.Map('map', {
                zoom: 12, center: [116.318927, 39.998043],
                resizeEnable: true, viewMode: '3D'
            });
            map.addControl(new AMap.ToolBar()); map.addControl(new AMap.Scale());
            var mapMarkers = []; 
            markersData.forEach(function(item) {
                var markerContent, position = new AMap.LngLat(item.position[0], item.position[1]);
                var color = '#e74c3c'; 
                if (item.icon === 'center') color = '#2ecc71'; 
                else if (item.icon === 'location') color = '#3498db'; 
                
                markerContent = `<div style="background-color: ${color}; width: 24px; height: 24px; border-radius: 12px; border: 2px solid white; box-shadow: 0 0 5px rgba(0,0,0,0.3);"></div>`;
                
                var marker = new AMap.Marker({
                    position: position, content: markerContent,
                    title: item.name, anchor: 'center', offset: new AMap.Pixel(0, 0)
                });
                var infoWindow = new AMap.InfoWindow({
                    content: '<div style="padding:10px;font-size:14px;">' + item.name + '</div>',
                    offset: new AMap.Pixel(0, -12)
                });
                marker.on('click', function() { infoWindow.open(map, marker.getPosition()); });
                mapMarkers.push(marker);
                marker.setMap(map);
            });
            if (markersData.length > 1) {
                var pathCoordinates = [];
                markersData.filter(item => item.icon !== 'place').forEach(function(item) { 
                    pathCoordinates.push(new AMap.LngLat(item.position[0], item.position[1]));
                });
                if (pathCoordinates.length > 1) { 
                    var polyline = new AMap.Polyline({
                        path: pathCoordinates, strokeColor: '#3498db', strokeWeight: 4,
                        strokeStyle: 'dashed', strokeDasharray: [5, 5], lineJoin: 'round'
                    });
                    polyline.setMap(map);
                }
            }
            if (mapMarkers.length > 0) { 
                 map.setFitView(mapMarkers);
            }
        }
        function animateCafeCards() {
            const cards = document.querySelectorAll('.cafe-card');
            if ('IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.style.opacity = 1;
                            entry.target.style.transform = 'translateY(0)';
                            observer.unobserve(entry.target);
                        }
                    });
                }, { threshold: 0.1 });
                cards.forEach((card, index) => {
                    card.style.opacity = 0; card.style.transform = 'translateY(30px)';
                    card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    card.style.transitionDelay = (index * 0.1) + 's';
                    observer.observe(card);
                });
            } else {
                cards.forEach((card, index) => {
                    card.style.opacity = 0; card.style.transform = 'translateY(30px)';
                    card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    setTimeout(() => { card.style.opacity = 1; card.style.transform = 'translateY(0)'; }, 300 + (index * 100));
                });
            }
        }
    </script>
</body>
</html>