# app/mcp/amap_mcp_client_sse.py
import asyncio
import json
import aiohttp
from typing import Dict, Any, List, Optional
from app.logger import logger

class AmapMCPClientSSE:
    """高德地图 MCP 服务客户端 - 支持 SSE 协议"""
    
    def __init__(self):
        self.url = "https://mcp.api-inference.modelscope.net/9b0135aaa8d748/sse"
        self.session = None
        self.connected = False
        self.request_id = 1
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            headers={
                "Accept": "text/event-stream, application/json",
                "Cache-Control": "no-cache",
                "Connection": "keep-alive"
            },
            timeout=aiohttp.ClientTimeout(total=30)
        )
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.session:
            await self.session.close()
    
    async def connect(self) -> bool:
        """连接到 SSE MCP 服务"""
        try:
            logger.info("🔌 尝试连接到高德地图 MCP SSE 服务...")
            
            # 对于 SSE 端点，先尝试简单的 GET 请求
            async with self.session.get(self.url) as response:
                if response.status == 200:
                    self.connected = True
                    logger.info("✅ 成功连接到高德地图 MCP SSE 服务")
                    return True
                else:
                    # 如果直接访问失败，尝试带参数的请求
                    logger.warning(f"直接连接状态码: {response.status}, 尝试带参数连接...")
                    return await self._connect_with_params()
                    
        except Exception as e:
            logger.error(f"❌ 连接高德地图 MCP 服务异常: {e}")
            return False
    
    async def _connect_with_params(self) -> bool:
        """带参数连接"""
        try:
            # 构建查询参数
            params = {
                "action": "initialize",
                "client": "MeetSpot"
            }
            
            async with self.session.get(self.url, params=params) as response:
                if response.status == 200:
                    self.connected = True
                    logger.info("✅ 带参数连接成功")
                    return True
                else:
                    logger.error(f"❌ 带参数连接失败，状态码: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 带参数连接异常: {e}")
            return False
    
    async def call_tool_via_get(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """通过 GET 参数调用工具"""
        try:
            self.request_id += 1
            
            # 将工具调用信息编码为 URL 参数
            params = {
                "tool": tool_name,
                "args": json.dumps(arguments, ensure_ascii=False),
                "id": self.request_id
            }
            
            logger.info(f"📡 调用工具: {tool_name}")
            
            async with self.session.get(self.url, params=params) as response:
                if response.status == 200:
                    content = await response.text()
                    logger.info(f"✅ 工具调用响应: {tool_name}")
                    
                    # 尝试解析响应
                    try:
                        # 检查是否是 SSE 格式
                        if "data:" in content:
                            # 处理 SSE 格式的响应
                            lines = content.strip().split('\n')
                            for line in lines:
                                if line.startswith('data: '):
                                    json_data = line[6:].strip()
                                    if json_data and json_data != '[DONE]':
                                        return json.loads(json_data)
                        else:
                            # 尝试直接解析为 JSON
                            return json.loads(content)
                        
                        # 如果都不是，返回原始内容
                        return {"content": content, "tool": tool_name, "status": "raw"}
                        
                    except json.JSONDecodeError:
                        return {"content": content, "tool": tool_name, "status": "text"}
                else:
                    error_text = await response.text()
                    logger.error(f"❌ 工具调用失败: {tool_name}, 状态码: {response.status}")
                    return {"error": f"HTTP {response.status}: {error_text[:200]}"}
                    
        except Exception as e:
            logger.error(f"❌ 工具调用异常: {tool_name}, 错误: {e}")
            return {"error": str(e)}
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用 MCP 工具的主要方法"""
        if not self.connected:
            logger.warning("MCP 服务未连接，尝试重新连接...")
            await self.connect()
        
        return await self.call_tool_via_get(tool_name, arguments)
    
    async def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用的工具列表"""
        try:
            params = {
                "action": "list_tools",
                "format": "json"
            }
            
            async with self.session.get(self.url, params=params) as response:
                if response.status == 200:
                    content = await response.text()
                    
                    try:
                        if "data:" in content:
                            lines = content.strip().split('\n')
                            for line in lines:
                                if line.startswith('data: '):
                                    json_data = line[6:].strip()
                                    if json_data and json_data != '[DONE]':
                                        result = json.loads(json_data)
                                        tools = result.get("tools", [])
                                        logger.info(f"📋 获取到 {len(tools)} 个可用工具")
                                        return tools
                        else:
                            result = json.loads(content)
                            tools = result.get("tools", [])
                            return tools
                        
                        # 如果解析失败，返回默认工具列表
                        default_tools = [
                            {"name": "maps_geo", "description": "地理编码"},
                            {"name": "maps_regeocode", "description": "逆地理编码"},
                            {"name": "maps_around_search", "description": "周边搜索"},
                            {"name": "maps_text_search", "description": "关键词搜索"},
                            {"name": "maps_distance", "description": "距离计算"}
                        ]
                        logger.info(f"📋 使用默认工具列表: {len(default_tools)} 个工具")
                        return default_tools
                        
                    except json.JSONDecodeError:
                        logger.warning("工具列表解析失败，使用默认列表")
                        return []
                else:
                    logger.error(f"❌ 获取工具列表失败，状态码: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"❌ 获取工具列表异常: {e}")
            return []

class AmapMCPService:
    """高德地图 MCP 服务包装器 - 使用 SSE 客户端"""
    
    def __init__(self):
        self.client = AmapMCPClientSSE()
        self.available_tools = []
    
    async def initialize(self) -> bool:
        """初始化服务"""
        try:
            async with self.client as client:
                # 获取可用工具
                self.available_tools = await client.get_available_tools()
                success = len(self.available_tools) > 0 or client.connected
                
                if success:
                    logger.info("🚀 MCP 服务初始化成功")
                else:
                    logger.warning("⚠️ MCP 服务连接成功但无可用工具")
                
                return success
        except Exception as e:
            logger.error(f"❌ MCP 服务初始化失败: {e}")
            return False
    
    async def geocode(self, address: str, city: str = "") -> Dict[str, Any]:
        """地理编码：地址转坐标"""
        async with self.client as client:
            return await client.call_tool(
                "maps_geo",
                {
                    "address": address,
                    "city": city
                }
            )
    
    async def reverse_geocode(self, location: str) -> Dict[str, Any]:
        """逆地理编码：坐标转地址"""
        async with self.client as client:
            return await client.call_tool(
                "maps_regeocode",
                {
                    "location": location
                }
            )
    
    async def search_around(self, location: str, keywords: str, radius: str = "3000") -> Dict[str, Any]:
        """周边搜索"""
        async with self.client as client:
            return await client.call_tool(
                "maps_around_search",
                {
                    "location": location,
                    "keywords": keywords,
                    "radius": radius
                }
            )
    
    async def text_search(self, keywords: str, city: str = "", types: str = "") -> Dict[str, Any]:
        """关键词搜索"""
        async with self.client as client:
            return await client.call_tool(
                "maps_text_search",
                {
                    "keywords": keywords,
                    "city": city,
                    "types": types
                }
            )
    
    async def calculate_distance(self, origins: str, destination: str, type: str = "1") -> Dict[str, Any]:
        """距离计算"""
        async with self.client as client:
            return await client.call_tool(
                "maps_distance",
                {
                    "origins": origins,
                    "destination": destination,
                    "type": type
                }
            )
