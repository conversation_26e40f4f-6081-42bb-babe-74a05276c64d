#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Base tool class for MeetSpot application
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List
from pydantic import BaseModel


class ToolResult(BaseModel):
    """Tool execution result"""
    output: str
    success: bool = True
    error: str = ""
    
    def __init__(self, output: str, success: bool = True, error: str = ""):
        super().__init__(output=output, success=success, error=error)


class BaseTool(BaseModel, ABC):
    """Base class for all tools"""
    
    name: str
    description: str
    parameters: Dict[str, Any] = {}
    
    @abstractmethod
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the tool with given parameters"""
        pass
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for API documentation"""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters
        }