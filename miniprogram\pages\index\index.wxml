<view class="container">
  <view class="logo">
    <view class="logo-title">MeetSpot</view>
    <view class="logo-subtitle">寻找最适合您的会面地点</view>
  </view>
  
  <view class="search-card">
    <view class="section-title">
      <view class="section-icon"></view>寻找最佳会面点
    </view>
    
    <form bindsubmit="submitForm">
      <view class="form-group" id="locationContainer">
        <view class="label">参与者地点：</view>
        <block wx:for="{{locations}}" wx:key="index">
          <view class="location-inputs">
            <view class="location-icon"></view>
            <input type="text" name="locations[{{index}}]" placeholder="例如：北京朝阳区望京宝星园" 
                   value="{{item}}" 
                   bindinput="updateLocation" 
                   data-index="{{index}}" />
            <view class="location-remove" bindtap="removeLocation" data-index="{{index}}" 
                  wx:if="{{locations.length > 2}}">×</view>
          </view>
        </block>
      </view>
      
      <view class="btn btn-light" bindtap="addLocation">
        <view class="btn-icon"></view>添加更多地点
      </view>
      
      <view class="form-group">
        <view class="label">选择场景类型（可多选）：</view>
        <view class="place-type-selector">
          <view class="place-type-option {{placeTypes.cafe ? 'selected' : ''}}" bindtap="togglePlaceType" data-type="cafe">
            <view class="place-type-name">
              <view class="place-type-icon cafe"></view>咖啡馆
            </view>
          </view>
          <view class="place-type-option {{placeTypes.restaurant ? 'selected' : ''}}" bindtap="togglePlaceType" data-type="restaurant">
            <view class="place-type-name">
              <view class="place-type-icon restaurant"></view>餐厅
            </view>
          </view>
          <view class="place-type-option {{placeTypes.library ? 'selected' : ''}}" bindtap="togglePlaceType" data-type="library">
            <view class="place-type-name">
              <view class="place-type-icon library"></view>图书馆
            </view>
          </view>
          <view class="place-type-option {{placeTypes.mall ? 'selected' : ''}}" bindtap="togglePlaceType" data-type="mall">
            <view class="place-type-name">
              <view class="place-type-icon mall"></view>商场
            </view>
          </view>
          <view class="place-type-option {{placeTypes.park ? 'selected' : ''}}" bindtap="togglePlaceType" data-type="park">
            <view class="place-type-name">
              <view class="place-type-icon park"></view>公园
            </view>
          </view>
        </view>
        <input type="text" name="keywords" placeholder="自定义关键词（可选）" style="margin-top: 20rpx;"
               value="{{keywords}}" bindinput="updateKeywords" />
      </view>
      
      <view class="form-group">
        <view class="label">特殊需求（可选）：</view>
        <textarea name="user_requirements" placeholder="例如：停车方便、环境安静、适合商务会谈等" 
                  value="{{userRequirements}}" bindinput="updateRequirements" />
      </view>
      
      <view class="tip-card">
        <view class="tip-text">小贴士：输入至少两个地点，选择您感兴趣的场景类型，我们会自动计算最佳会面点并推荐附近的场所。</view>
      </view>
      
      <button class="btn" form-type="submit" loading="{{isLoading}}">
        <view class="btn-icon"></view>查找最佳会面点
      </button>
    </form>
  </view>
  
  <view class="feature-cards">
    <view class="feature-card">
      <view class="feature-icon location-analysis"></view>
      <view class="feature-title">智能位置分析</view>
      <view class="feature-text">自动计算最平衡的会面地点，让每个人的出行距离相对均衡</view>
    </view>
    <view class="feature-card">
      <view class="feature-icon place-variety"></view>
      <view class="feature-title">多样场所选择</view>
      <view class="feature-text">从咖啡馆、餐厅到图书馆、商场，根据您的需求推荐合适场所</view>
    </view>
    <view class="feature-card">
      <view class="feature-icon interactive-map"></view>
      <view class="feature-title">交互式地图</view>
      <view class="feature-text">直观地在地图上展示所有地点和推荐咖啡馆，方便导航</view>
    </view>
  </view>
</view> 