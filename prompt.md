我们参加魔搭MCP&Agent2025挑战赛赛道二Agent开发，这是评分标准> '/mnt/d/all_code/MeetSpot/image.png'      然后你可以在这里查看评分标准。仔细优化一下整个作品

/analyze --code --persona-architect    # Systems thinking approach / 系统思维方法
/build --react --persona-frontend      # UX-focused development / 注重用户体验的开发

/review --quality --evidence --persona-qa    # AI-powered code review / 人工智能驱动的代码审查
/analyze --architecture --seq                # System analysis / 系统分析
/troubleshoot --prod --five-whys             # Issue resolution / 问题解决
/improve --performance --iterate             # Optimization / 优化

删除GitHub远程仓库，然后专注于魔塔仓库的开发