2025-07-02 13:57:48.633 | INFO     | app.mcp.amap_client_official:connect:43 - ✅ 成功连接到高德地图官方 MCP 服务
2025-07-02 13:57:48.692 | INFO     | app.mcp.amap_client_official:get_available_tools:95 - 原始返回: [{"jsonrpc": "2.0", "id": 2, "result": {"tools": [{"name": "maps_direction_bicycling", "description": "骑行路径规划用于规划骑行通勤方案，规划时会考虑天桥、单行线、封路等情况。最大支持 500km 的骑行路线规划", "inputSchema": {"type": "object", "prope...
2025-07-02 13:57:48.692 | INFO     | app.mcp.amap_client_official:get_available_tools:103 - 📋 获取到 15 个可用工具
2025-07-02 13:57:48.692 | INFO     | app.mcp.amap_client_official:initialize:135 - 🚀 高德地图 MCP 服务初始化成功，可用工具: 15 个
2025-07-02 13:57:48.762 | INFO     | app.mcp.amap_client_official:connect:43 - ✅ 成功连接到高德地图官方 MCP 服务
2025-07-02 13:57:48.829 | INFO     | app.mcp.amap_client_official:call_tool:72 - ✅ MCP 工具调用成功: maps_geo
2025-07-02 13:57:48.893 | INFO     | app.mcp.amap_client_official:connect:43 - ✅ 成功连接到高德地图官方 MCP 服务
2025-07-02 13:57:49.173 | INFO     | app.mcp.amap_client_official:call_tool:72 - ✅ MCP 工具调用成功: maps_text_search
2025-07-02 13:57:49.240 | INFO     | app.mcp.amap_client_official:connect:43 - ✅ 成功连接到高德地图官方 MCP 服务
2025-07-02 13:57:49.432 | INFO     | app.mcp.amap_client_official:call_tool:72 - ✅ MCP 工具调用成功: maps_around_search
2025-07-02 13:57:49.492 | INFO     | app.mcp.amap_client_official:connect:43 - ✅ 成功连接到高德地图官方 MCP 服务
2025-07-02 13:57:49.576 | INFO     | app.mcp.amap_client_official:call_tool:72 - ✅ MCP 工具调用成功: maps_weather
