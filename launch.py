#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MeetSpot 启动脚本
用于在 ModelScope 平台启动应用
"""

import os
import sys

# 确保模块路径正确
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    try:
        # 导入并启动应用
        from app import create_app
        
        # 创建并启动 Gradio 应用
        demo = create_app()
        
        # ModelScope 部署配置
        demo.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=False,
            show_error=True
        )
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
