Page({
  data: {
    locations: ['', ''], // 默认两个位置输入框
    keywords: '',
    userRequirements: '',
    isLoading: false,
    placeTypes: {
      cafe: true,
      restaurant: false,
      library: false,
      mall: false,
      park: false
    }
  },
  
  // 添加位置
  addLocation: function() {
    let locations = this.data.locations;
    locations.push('');
    this.setData({ locations });
  },
  
  // 删除位置
  removeLocation: function(e) {
    const { index } = e.currentTarget.dataset;
    let locations = this.data.locations;
    
    if (locations.length > 2) {
      locations.splice(index, 1);
      this.setData({ locations });
    }
  },
  
  // 更新位置输入
  updateLocation: function(e) {
    const { index } = e.currentTarget.dataset;
    const { value } = e.detail;
    let locations = this.data.locations;
    locations[index] = value;
    this.setData({ locations });
  },
  
  // 更新关键词
  updateKeywords: function(e) {
    this.setData({ keywords: e.detail.value });
  },
  
  // 更新需求
  updateRequirements: function(e) {
    this.setData({ userRequirements: e.detail.value });
  },
  
  // 切换场所类型
  togglePlaceType: function(e) {
    const { type } = e.currentTarget.dataset;
    let placeTypes = this.data.placeTypes;
    placeTypes[type] = !placeTypes[type];
    this.setData({ placeTypes });
  },
  
  // 提交表单
  submitForm: function(e) {
    // 验证位置输入
    const validLocations = this.data.locations.filter(loc => loc.trim() !== '');
    if (validLocations.length < 2) {
      wx.showToast({
        title: '请至少输入两个位置',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ isLoading: true });
    
    // 构建关键词
    let keywords = this.data.keywords || '';
    const selectedTypes = [];
    
    for (const type in this.data.placeTypes) {
      if (this.data.placeTypes[type]) {
        switch(type) {
          case 'cafe': selectedTypes.push('咖啡馆'); break;
          case 'restaurant': selectedTypes.push('餐厅'); break;
          case 'library': selectedTypes.push('图书馆'); break;
          case 'mall': selectedTypes.push('商场'); break;
          case 'park': selectedTypes.push('公园'); break;
        }
      }
    }
    
    if (selectedTypes.length > 0) {
      keywords = selectedTypes.join('、') + (keywords ? '、' + keywords : '');
    }
    
    // 模拟API调用
    setTimeout(() => {
      this.setData({ isLoading: false });
      
      // 构建示例数据
      const mockData = {
        center: {
          latitude: 39.960799,
          longitude: 116.4755145
        },
        locations: validLocations,
        keywords: keywords,
        userRequirements: this.data.userRequirements,
        places: [
          {
            name: "北京福楼法餐厅(三元桥店)",
            rating: 4.7,
            address: "霄云路18号",
            business_hours: "营业时间未知",
            phone: "010-65955135",
            distance: "0.5",
            tags: "鹅肝,牛排,三文鱼,生蚝"
          },
          {
            name: "霄云苑",
            rating: 4.6,
            address: "东三环霄云路18号",
            business_hours: "营业时间未知",
            phone: "010-64615858",
            distance: "0.5",
            tags: "霄云鲜鲍红烧肉,烤鸭,宫爆鸡丁"
          }
        ]
      };
      
      // 将数据传递到推荐页面
      wx.navigateTo({
        url: `/pages/recommendations/recommendations?data=${encodeURIComponent(JSON.stringify(mockData))}`
      });
    }, 1500);
  }
}); 