2025-06-29 14:32:05.192 | INFO     | __main__:performance_middleware:73 - 处理请求 #1: GET http://127.0.0.1:8000/
2025-06-29 14:32:05.195 | INFO     | __main__:performance_middleware:92 - 请求 #1 完成，耗时: 0.002秒
2025-06-29 14:32:05.323 | INFO     | __main__:performance_middleware:73 - 处理请求 #2: GET http://127.0.0.1:8000/workspace/meetspot_finder.html
2025-06-29 14:32:05.650 | INFO     | __main__:performance_middleware:92 - 请求 #2 完成，耗时: 0.327秒
2025-06-29 14:32:10.585 | INFO     | __main__:performance_middleware:73 - 处理请求 #3: GET http://127.0.0.1:8000/favicon.ico
2025-06-29 14:32:10.587 | INFO     | __main__:performance_middleware:92 - 请求 #3 完成，耗时: 0.002秒
2025-06-29 14:51:31.513 | INFO     | __main__:performance_middleware:73 - 处理请求 #4: POST http://127.0.0.1:8000/api/find_meetspot
2025-06-29 14:51:31.515 | INFO     | __main__:find_meetspot:315 - [req_1751179891515] API请求开始: locations=['北京大学', '清华大学'], keywords=健身房
2025-06-29 14:51:32.102 | INFO     | __main__:find_meetspot:371 - [req_1751179891515] API请求完成，耗时: 0.59秒
2025-06-29 14:51:32.103 | INFO     | __main__:performance_middleware:92 - 请求 #4 完成，耗时: 0.590秒
2025-06-29 14:51:32.140 | INFO     | __main__:performance_middleware:73 - 处理请求 #5: GET http://127.0.0.1:8000/workspace/js_src/place_recommendation_20250629145132_9da787a7.html
2025-06-29 14:51:32.142 | INFO     | __main__:performance_middleware:92 - 请求 #5 完成，耗时: 0.003秒
2025-06-29 15:53:20.828 | INFO     | __main__:performance_middleware:73 - 处理请求 #6: POST http://127.0.0.1:8000/api/find_meetspot
2025-06-29 15:53:20.830 | INFO     | __main__:find_meetspot:315 - [req_1751183600830] API请求开始: locations=['北京大学', '清华大学', '北京中关村'], keywords=景点 KTV 购物中心
2025-06-29 15:53:21.047 | INFO     | app.tool.meetspot_recommender:execute:320 - 多场景并发搜索: ['景点', 'KTV', '购物中心']
2025-06-29 15:53:21.047 | INFO     | app.tool.meetspot_recommender:search_keyword:324 - 开始搜索场景: '景点'
2025-06-29 15:53:21.048 | INFO     | app.tool.meetspot_recommender:search_keyword:324 - 开始搜索场景: 'KTV'
2025-06-29 15:53:21.048 | INFO     | app.tool.meetspot_recommender:search_keyword:324 - 开始搜索场景: '购物中心'
2025-06-29 15:53:21.180 | INFO     | app.tool.meetspot_recommender:search_keyword:335 - 'KTV' 找到 20 个结果
2025-06-29 15:53:21.201 | INFO     | app.tool.meetspot_recommender:search_keyword:335 - '景点' 找到 20 个结果
2025-06-29 15:53:21.224 | INFO     | app.tool.meetspot_recommender:search_keyword:335 - '购物中心' 找到 11 个结果
2025-06-29 15:53:21.225 | INFO     | app.tool.meetspot_recommender:execute:366 - 多场景搜索完成，去重后共 51 个结果
2025-06-29 15:53:21.225 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 恬园草坪 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.225 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学邱德拔体育馆北广场 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.225 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 成府园 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.225 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学博雅塔 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.225 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 创新文化广场 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.225 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 未名湖 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.225 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 中海体育公园 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.225 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学埃德加·斯诺之墓 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.225 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学慈济寺 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.225 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学花神庙 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.225 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 石舫(镜春路) 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.225 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学石舫(北京核磁共振中心西) 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.225 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 钱钟书清华故居 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.225 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学鲁斯亭 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 清华大学桂韵 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学临湖轩 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学石雕屏风 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 冯友兰故居 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 未名湖燕园建筑 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 燕南园 匹配场景 '景点'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 酷秀KTV(中关村店) 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 赛乐堡量贩KTV(五道口店) 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 友唱全民K歌(领展购物广场·中关村店) 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 星聚会ktv(中关村领展店) 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 来音客KTV 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 唱吧麦颂KTV(五道口店) 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 华丽会娱乐 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 酷秀KTV(五道口店) 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 聚宝轰趴馆·KTV棋牌·团建聚会(中关村店) 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 相遇轰趴·桌游·团建·KTV 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 桃屋量贩KTV 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 唱吧麦颂KTV(北京万柳华联店) 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 酷秀KTV(万柳店) 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 唱吧麦颂KTV(四通桥店) 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 今夜港 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 唱吧麦颂KTV(西海国际中心店) 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.226 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 唱吧麦颂KTV(汉华世纪大厦店) 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.227 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 唱吧麦颂KTV(上地华联店) 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.227 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 唱吧麦颂KTV(京果商厦店) 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.227 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 上地之城公馆 匹配场景 'KTV'，获得奖励分数
2025-06-29 15:53:21.227 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 中关村科贸电子城 匹配场景 '购物中心'，获得奖励分数
2025-06-29 15:53:21.227 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 融科资讯中心商业 匹配场景 '购物中心'，获得奖励分数
2025-06-29 15:53:21.227 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 中关村广场购物中心D区 匹配场景 '购物中心'，获得奖励分数
2025-06-29 15:53:21.227 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 中关村大融城(西区) 匹配场景 '购物中心'，获得奖励分数
2025-06-29 15:53:21.227 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 京颐商场 匹配场景 '购物中心'，获得奖励分数
2025-06-29 15:53:21.227 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京中关村领展广场 匹配场景 '购物中心'，获得奖励分数
2025-06-29 15:53:21.227 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京中关村领展广场 匹配场景 '购物中心'，获得奖励分数
2025-06-29 15:53:21.227 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 新中关购物中心B座 匹配场景 '购物中心'，获得奖励分数
2025-06-29 15:53:21.227 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 新中关购物中心 匹配场景 '购物中心'，获得奖励分数
2025-06-29 15:53:21.227 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 新中关购物中心A座 匹配场景 '购物中心'，获得奖励分数
2025-06-29 15:53:21.227 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 中关村大融城(东区)(装修中) 匹配场景 '购物中心'，获得奖励分数
2025-06-29 15:53:21.227 | INFO     | app.tool.meetspot_recommender:_rank_places:520 - 应用多场景平衡策略
2025-06-29 15:53:21.227 | INFO     | app.tool.meetspot_recommender:_rank_places:536 - 从场景 '景点' 选择了 2 个场所
2025-06-29 15:53:21.228 | INFO     | app.tool.meetspot_recommender:_rank_places:536 - 从场景 'KTV' 选择了 2 个场所
2025-06-29 15:53:21.228 | INFO     | app.tool.meetspot_recommender:_rank_places:536 - 从场景 '购物中心' 选择了 2 个场所
2025-06-29 15:53:21.231 | INFO     | __main__:find_meetspot:371 - [req_1751183600830] API请求完成，耗时: 0.40秒
2025-06-29 15:53:21.231 | INFO     | __main__:performance_middleware:92 - 请求 #6 完成，耗时: 0.403秒
2025-06-29 15:53:21.257 | INFO     | __main__:performance_middleware:73 - 处理请求 #7: GET http://127.0.0.1:8000/workspace/js_src/place_recommendation_20250629155321_1f2a3b63.html
2025-06-29 15:53:21.259 | INFO     | __main__:performance_middleware:92 - 请求 #7 完成，耗时: 0.002秒
2025-06-29 15:54:04.444 | INFO     | __main__:performance_middleware:73 - 处理请求 #8: POST http://127.0.0.1:8000/api/find_meetspot
2025-06-29 15:54:04.445 | INFO     | __main__:find_meetspot:315 - [req_1751183644445] API请求开始: locations=['北京大学', '清华大学', '北京中关村', '北京三里屯'], keywords=酒吧
2025-06-29 15:54:04.675 | ERROR    | app.tool.meetspot_recommender:_geocode:416 - 地理编码失败: CUQPS_HAS_EXCEEDED_THE_LIMIT, 地址: 北京三里屯
2025-06-29 15:54:04.676 | WARNING  | __main__:find_meetspot:354 - [req_1751183644445] 无法生成HTML页面
2025-06-29 15:54:04.677 | INFO     | __main__:performance_middleware:92 - 请求 #8 完成，耗时: 0.233秒
2025-06-29 15:54:08.950 | INFO     | __main__:performance_middleware:73 - 处理请求 #9: POST http://127.0.0.1:8000/api/find_meetspot
2025-06-29 15:54:08.951 | INFO     | __main__:find_meetspot:315 - [req_1751183648951] API请求开始: locations=['北京大学', '清华大学', '北京中关村'], keywords=酒吧
2025-06-29 15:54:09.299 | INFO     | __main__:find_meetspot:371 - [req_1751183648951] API请求完成，耗时: 0.35秒
2025-06-29 15:54:09.300 | INFO     | __main__:performance_middleware:92 - 请求 #9 完成，耗时: 0.350秒
2025-06-29 15:54:09.326 | INFO     | __main__:performance_middleware:73 - 处理请求 #10: GET http://127.0.0.1:8000/workspace/js_src/place_recommendation_20250629155409_48a7fb55.html
2025-06-29 15:54:09.328 | INFO     | __main__:performance_middleware:92 - 请求 #10 完成，耗时: 0.002秒
2025-06-29 16:02:50.896 | INFO     | __main__:performance_middleware:73 - 处理请求 #11: POST http://127.0.0.1:8000/api/find_meetspot
2025-06-29 16:02:50.897 | INFO     | __main__:find_meetspot:315 - [req_1751184170897] API请求开始: locations=['北京大学', '清华大学', '北京中关村'], keywords=公园
2025-06-29 16:02:51.259 | INFO     | __main__:find_meetspot:371 - [req_1751184170897] API请求完成，耗时: 0.36秒
2025-06-29 16:02:51.259 | INFO     | __main__:performance_middleware:92 - 请求 #11 完成，耗时: 0.364秒
2025-06-29 16:02:51.288 | INFO     | __main__:performance_middleware:73 - 处理请求 #12: GET http://127.0.0.1:8000/workspace/js_src/place_recommendation_20250629160251_58edb45a.html
2025-06-29 16:02:51.291 | INFO     | __main__:performance_middleware:92 - 请求 #12 完成，耗时: 0.003秒
2025-06-29 16:15:07.176 | INFO     | __main__:performance_middleware:73 - 处理请求 #13: POST http://127.0.0.1:8000/api/find_meetspot
2025-06-29 16:15:07.177 | INFO     | __main__:find_meetspot:315 - [req_1751184907177] API请求开始: locations=['北京大学', '清华大学', '北京中关村'], keywords=图书馆
2025-06-29 16:15:07.566 | INFO     | __main__:find_meetspot:371 - [req_1751184907177] API请求完成，耗时: 0.39秒
2025-06-29 16:15:07.566 | INFO     | __main__:performance_middleware:92 - 请求 #13 完成，耗时: 0.391秒
2025-06-29 16:15:07.601 | INFO     | __main__:performance_middleware:73 - 处理请求 #14: GET http://127.0.0.1:8000/workspace/js_src/place_recommendation_20250629161507_2471f1f3.html
2025-06-29 16:15:07.604 | INFO     | __main__:performance_middleware:92 - 请求 #14 完成，耗时: 0.002秒
2025-06-29 16:16:23.873 | INFO     | __main__:performance_middleware:73 - 处理请求 #15: POST http://127.0.0.1:8000/api/find_meetspot
2025-06-29 16:16:23.874 | INFO     | __main__:find_meetspot:315 - [req_1751184983874] API请求开始: locations=['北京大学', '清华大学', '北京中关村'], keywords=商场 景点
2025-06-29 16:16:24.079 | INFO     | app.tool.meetspot_recommender:execute:320 - 多场景并发搜索: ['商场', '景点']
2025-06-29 16:16:24.079 | INFO     | app.tool.meetspot_recommender:search_keyword:324 - 开始搜索场景: '商场'
2025-06-29 16:16:24.080 | INFO     | app.tool.meetspot_recommender:search_keyword:324 - 开始搜索场景: '景点'
2025-06-29 16:16:24.237 | INFO     | app.tool.meetspot_recommender:search_keyword:335 - '景点' 找到 20 个结果
2025-06-29 16:16:24.256 | INFO     | app.tool.meetspot_recommender:search_keyword:335 - '商场' 找到 17 个结果
2025-06-29 16:16:24.256 | INFO     | app.tool.meetspot_recommender:execute:366 - 多场景搜索完成，去重后共 37 个结果
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 中关村科贸电子城 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 融科资讯中心商业 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 中关村广场购物中心D区 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 中关村大融城(西区) 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 京颐商场 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 巴巴商城 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京中关村领展广场 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京中关村领展广场 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 新中关购物中心B座 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 新中关购物中心 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 新中关购物中心A座 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 五道口购物中心 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 海淀社区商业中心 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 大成仓网球商城(北航北门店) 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 艾瑟顿商业广场 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 龙湖北京颐和星悦荟 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 中关村大融城(东区)(装修中) 匹配场景 '商场'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 恬园草坪 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学邱德拔体育馆北广场 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 成府园 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学博雅塔 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.257 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 创新文化广场 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 未名湖 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 中海体育公园 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学埃德加·斯诺之墓 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学慈济寺 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学花神庙 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 石舫(镜春路) 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学石舫(北京核磁共振中心西) 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 钱钟书清华故居 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学鲁斯亭 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 清华大学桂韵 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学临湖轩 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 北京大学石雕屏风 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 冯友兰故居 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 未名湖燕园建筑 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | DEBUG    | app.tool.meetspot_recommender:_rank_places:503 - 场所 燕南园 匹配场景 '景点'，获得奖励分数
2025-06-29 16:16:24.258 | INFO     | app.tool.meetspot_recommender:_rank_places:520 - 应用多场景平衡策略
2025-06-29 16:16:24.259 | INFO     | app.tool.meetspot_recommender:_rank_places:536 - 从场景 '景点' 选择了 4 个场所
2025-06-29 16:16:24.259 | INFO     | app.tool.meetspot_recommender:_rank_places:536 - 从场景 '商场' 选择了 4 个场所
2025-06-29 16:16:24.262 | INFO     | __main__:find_meetspot:371 - [req_1751184983874] API请求完成，耗时: 0.39秒
2025-06-29 16:16:24.262 | INFO     | __main__:performance_middleware:92 - 请求 #15 完成，耗时: 0.389秒
2025-06-29 16:16:24.301 | INFO     | __main__:performance_middleware:73 - 处理请求 #16: GET http://127.0.0.1:8000/workspace/js_src/place_recommendation_20250629161624_58542676.html
2025-06-29 16:16:24.303 | INFO     | __main__:performance_middleware:92 - 请求 #16 完成，耗时: 0.003秒
2025-06-29 17:14:21.077 | INFO     | __main__:performance_middleware:73 - 处理请求 #17: POST http://127.0.0.1:8000/api/find_meetspot
2025-06-29 17:14:21.078 | INFO     | __main__:find_meetspot:315 - [req_1751188461078] API请求开始: locations=['北京大学', '清华大学', '北京中关村'], keywords=公园
2025-06-29 17:14:21.463 | INFO     | __main__:find_meetspot:371 - [req_1751188461078] API请求完成，耗时: 0.38秒
2025-06-29 17:14:21.463 | INFO     | __main__:performance_middleware:92 - 请求 #17 完成，耗时: 0.386秒
2025-06-29 17:14:21.497 | INFO     | __main__:performance_middleware:73 - 处理请求 #18: GET http://127.0.0.1:8000/workspace/js_src/place_recommendation_20250629171421_fc30e166.html
2025-06-29 17:14:21.500 | INFO     | __main__:performance_middleware:92 - 请求 #18 完成，耗时: 0.003秒
