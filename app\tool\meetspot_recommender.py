import asyncio
import json
import math
import os
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import aiofiles
import aiohttp
from pydantic import Field

from app.logger import logger
from app.tool.base import BaseTool, ToolResult
from app.config import config
from app.services.realtime_data import realtime_service
from app.services.preference_engine import preference_engine


class CafeRecommender(BaseTool):
    """智能多人会面场所推荐工具，支持3-10人最优会面点计算"""

    name: str = "meetspot_recommender"
    description: str = """智能多人会面场所推荐系统。
支持3-10人最优会面地点计算，基于加权距离、交通便利性、场所容量等多维度算法。
集成实时数据：天气状况、交通拥堵、营业状态、用户评价等。
工具会生成包含地图和推荐信息的HTML页面，提供详细的场所信息、地理位置和交通建议。
支持各种场景：商务会议、朋友聚会、家庭聚餐、约会、运动健身等。
"""
    parameters: dict = {
        "type": "object",
        "properties": {
            "locations": {
                "type": "array",
                "description": "(必填) 所有参与者的位置描述列表，支持3-10人会面，如['北京朝阳区望京宝星园', '海淀中关村地铁站', '通州万达广场']",
                "items": {"type": "string"},
                "minItems": 1,
                "maxItems": 10
            },
            "participant_weights": {
                "type": "array",
                "description": "(可选) 参与者权重列表，对应locations的重要性权重，如[1.0, 1.5, 1.0]表示第二个人的位置更重要",
                "items": {"type": "number"},
                "default": []
            },
            "keywords": {
                "type": "string",
                "description": "(可选) 搜索关键词，如'咖啡馆'、'篮球场'、'电影院'、'商场'等",
                "default": "咖啡馆",
            },
            "meeting_scenario": {
                "type": "string",
                "description": "(可选) 会面场景类型：商务会议、朋友聚会、家庭聚餐、约会、运动健身",
                "enum": ["商务会议", "朋友聚会", "家庭聚餐", "约会", "运动健身", "其他"],
                "default": "朋友聚会"
            },
            "group_size": {
                "type": "integer",
                "description": "(可选) 预期参与人数，用于场所容量筛选",
                "minimum": 1,
                "maximum": 20,
                "default": 2
            },
            "user_requirements": {
                "type": "string",
                "description": "(可选) 用户的额外需求，如'停车方便'，'环境安静'，'有包间'等",
                "default": "",
            },
            "time_preference": {
                "type": "string",
                "description": "(可选) 时间偏好，如'工作日晚上'，'周末下午'，用于营业时间和拥堵状况判断",
                "default": ""
            }
        },
        "required": ["locations"],
    }

    # 高德地图API密钥
    api_key: str = Field(default="")

    # 缓存请求结果以减少API调用
    geocode_cache: Dict[str, Dict] = Field(default_factory=dict)
    poi_cache: Dict[str, List] = Field(default_factory=dict)

    PLACE_TYPE_CONFIG: Dict[str, Dict[str, str]] = {
        "咖啡馆": {
            "topic": "咖啡会",
            "icon_header": "bxs-coffee-togo",
            "icon_section": "bx-coffee",
            "icon_card": "bxs-coffee-alt",
            "map_legend": "咖啡馆",
            "noun_singular": "咖啡馆",
            "noun_plural": "咖啡馆",
            "theme_primary": "#9c6644", # 棕色系
            "theme_primary_light": "#c68b59",
            "theme_primary_dark": "#7f5539",
            "theme_secondary": "#c9ada7",
            "theme_light": "#f2e9e4",
            "theme_dark": "#22223b",
        },
        "图书馆": {
            "topic": "知书达理会",
            "icon_header": "bxs-book",
            "icon_section": "bx-book",
            "icon_card": "bxs-book-reader",
            "map_legend": "图书馆",
            "noun_singular": "图书馆",
            "noun_plural": "图书馆",
            "theme_primary": "#4a6fa5", # 蓝色系
            "theme_primary_light": "#6e8fc5",
            "theme_primary_dark": "#305182",
            "theme_secondary": "#9dc0e5",
            "theme_light": "#f0f5fa",
            "theme_dark": "#2c3e50",
        },
        "餐厅": {
            "topic": "美食汇",
            "icon_header": "bxs-restaurant",
            "icon_section": "bx-restaurant",
            "icon_card": "bxs-restaurant",
            "map_legend": "餐厅",
            "noun_singular": "餐厅",
            "noun_plural": "餐厅",
            "theme_primary": "#e74c3c", # 红色系
            "theme_primary_light": "#f1948a",
            "theme_primary_dark": "#c0392b",
            "theme_secondary": "#fadbd8",
            "theme_light": "#fef5e7",
            "theme_dark": "#34222e",
        },
        "商场": {
            "topic": "乐购汇",
            "icon_header": "bxs-shopping-bag",
            "icon_section": "bx-shopping-bag",
            "icon_card": "bxs-store-alt",
            "map_legend": "商场",
            "noun_singular": "商场",
            "noun_plural": "商场",
            "theme_primary": "#8e44ad", # 紫色系
            "theme_primary_light": "#af7ac5",
            "theme_primary_dark": "#6c3483",
            "theme_secondary": "#d7bde2",
            "theme_light": "#f4ecf7",
            "theme_dark": "#3b1f2b",
        },
        "公园": {
            "topic": "悠然汇",
            "icon_header": "bxs-tree",
            "icon_section": "bx-leaf",
            "icon_card": "bxs-florist",
            "map_legend": "公园",
            "noun_singular": "公园",
            "noun_plural": "公园",
            "theme_primary": "#27ae60", # 绿色系
            "theme_primary_light": "#58d68d",
            "theme_primary_dark": "#1e8449",
            "theme_secondary": "#a9dfbf",
            "theme_light": "#eafaf1",
            "theme_dark": "#1e3b20",
        },
        "电影院": {
            "topic": "光影汇",
            "icon_header": "bxs-film",
            "icon_section": "bx-film",
            "icon_card": "bxs-movie-play",
            "map_legend": "电影院",
            "noun_singular": "电影院",
            "noun_plural": "电影院",
            "theme_primary": "#34495e", # 深蓝灰色系
            "theme_primary_light": "#5d6d7e",
            "theme_primary_dark": "#2c3e50",
            "theme_secondary": "#aeb6bf",
            "theme_light": "#ebedef",
            "theme_dark": "#17202a",
        },
        "篮球场": {
            "topic": "篮球部落",
            "icon_header": "bxs-basketball",
            "icon_section": "bx-basketball",
            "icon_card": "bxs-basketball",
            "map_legend": "篮球场",
            "noun_singular": "篮球场",
            "noun_plural": "篮球场",
            "theme_primary": "#f39c12", # 橙色系
            "theme_primary_light": "#f8c471",
            "theme_primary_dark": "#d35400",
            "theme_secondary": "#fdebd0",
            "theme_light": "#fef9e7",
            "theme_dark": "#4a2303",
        },
        "default": { # 默认主题颜色 (同咖啡馆)
            "topic": "会面点",
            "icon_header": "bxs-map-pin",
            "icon_section": "bx-map-pin",
            "icon_card": "bxs-location-plus",
            "map_legend": "场所",
            "noun_singular": "场所",
            "noun_plural": "场所",
            "theme_primary": "#9c6644",
            "theme_primary_light": "#c68b59",
            "theme_primary_dark": "#7f5539",
            "theme_secondary": "#c9ada7",
            "theme_light": "#f2e9e4",
            "theme_dark": "#22223b",
        }
    }

    def _get_place_config(self, primary_keyword: str) -> Dict[str, str]:
        """获取指定场所类型的显示配置"""
        return self.PLACE_TYPE_CONFIG.get(primary_keyword, self.PLACE_TYPE_CONFIG["default"])

    async def execute(
        self,
        locations: List[str],
        participant_weights: List[float] = None,
        keywords: str = "咖啡馆",
        meeting_scenario: str = "朋友聚会",
        group_size: int = 2,
        user_requirements: str = "",
        time_preference: str = "",
        place_type: str = "",  # 保持向后兼容
    ) -> ToolResult:
        # 优先使用MCP服务
        try:
            from app.mcp.amap_mcp_client import AmapMCPService
            mcp_service = AmapMCPService(use_official=False)
            if await mcp_service.initialize():
                logger.info("✅ 使用高德地图MCP服务")
                return await self._execute_with_mcp(mcp_service, locations, participant_weights, keywords, meeting_scenario, group_size, user_requirements, time_preference)
            else:
                logger.warning("⚠️ MCP服务初始化失败，回退到原生API")
        except Exception as e:
            logger.error(f"❌ MCP服务异常: {e}")
            logger.info("回退到原生API")
        
        # 原生API实现
        return await self._execute_with_native_api(locations, participant_weights, keywords, meeting_scenario, group_size, user_requirements, time_preference)
    
    async def _execute_with_mcp(self, mcp_service, locations: List[str], participant_weights: List[float], keywords: str, meeting_scenario: str, group_size: int, user_requirements: str, time_preference: str) -> ToolResult:
        """使用MCP服务执行推荐"""
        try:
            coordinates = []
            location_info = []
            
            for location in locations:
                # 使用MCP服务进行地理编码
                geocode_result = await mcp_service.geocode(location)
                
                if geocode_result.get("error"):
                    logger.error(f"MCP地理编码失败: {location}, 错误: {geocode_result['error']}")
                    return ToolResult(output=f"无法找到地点: {location}")
                
                # 解析MCP返回的结果
                result_data = geocode_result.get("result", {})
                if not result_data.get("geocodes"):
                    return ToolResult(output=f"无法找到地点: {location}")
                
                geocode = result_data["geocodes"][0]
                location_str = geocode.get("location", "")
                if not location_str:
                    return ToolResult(output=f"无法获取地点坐标: {location}")
                
                lng, lat = location_str.split(",")
                coordinates.append((float(lng), float(lat)))
                location_info.append({
                    "name": location,
                    "formatted_address": geocode.get("formatted_address", location),
                    "location": location_str,
                    "lng": float(lng),
                    "lat": float(lat)
                })

            if not coordinates:
                return ToolResult(output="未能解析任何有效地点。")

            center_point = self._calculate_center_point(coordinates, participant_weights)
            
            # 使用MCP服务进行周边搜索
            search_result = await mcp_service.search_around(
                f"{center_point[0]},{center_point[1]}",
                keywords,
                radius="5000"
            )
            
            if search_result.get("error"):
                logger.error(f"MCP周边搜索失败: {search_result['error']}")
                return ToolResult(output=f"在计算的中心点附近找不到与 '{keywords}' 相关的场所。")
            
            # 解析搜索结果
            result_data = search_result.get("result", {})
            searched_places = result_data.get("pois", [])
            
            if not searched_places:
                return ToolResult(output=f"在计算的中心点附近找不到与 '{keywords}' 相关的场所。")

            # 集成实时数据
            logger.info("🌤️ 正在获取实时数据...")
            
            # 获取天气信息
            city_name = self._extract_city_name(locations)
            weather_info = await realtime_service.get_weather_info(city_name)
            
            # 获取交通分析
            traffic_analysis = await realtime_service.get_traffic_analysis(locations, center_point)
            
            # 获取营业状态信息
            searched_places = await realtime_service.get_business_hours_info(searched_places)

            recommended_places = self._rank_places(searched_places, center_point, user_requirements, keywords, meeting_scenario, group_size, weather_info, traffic_analysis)

            html_path = await self._generate_html_page(
                location_info,
                recommended_places,
                center_point,
                user_requirements,
                keywords,
                weather_info,
                traffic_analysis
            )
            result_text = self._format_result_text(location_info, recommended_places, html_path, keywords) 
            return ToolResult(output=result_text)

        except Exception as e:
            logger.exception(f"MCP推荐过程中发生错误: {str(e)}") 
            return ToolResult(output=f"推荐失败: {str(e)}")
    
    async def _apply_personalization(self, location_info: List[Dict], searched_places: List[Dict], 
                                   keywords: str, meeting_scenario: str, group_size: int, user_requirements: str) -> tuple:
        """应用个性化推荐逻辑"""
        logger.info("🎯 正在应用个性化推荐...")
        user_id = preference_engine.get_user_id([loc["name"] for loc in location_info], group_size)
        context = {
            "keywords": keywords,
            "meeting_scenario": meeting_scenario,
            "group_size": group_size,
            "user_requirements": user_requirements,
            "locations": [loc["name"] for loc in location_info]
        }
        
        # 获取个性化权重
        personalized_weights = preference_engine.generate_personalized_weights(user_id, context)
        
        # 获取个性化推荐理由
        personalized_recommendations = preference_engine.get_personalized_recommendations(user_id, searched_places, context)
        
        # 获取智能建议
        smart_suggestions = preference_engine.get_smart_suggestions(user_id, context)
        
        return personalized_weights, personalized_recommendations, smart_suggestions
    
    async def _execute_with_native_api(self, locations: List[str], participant_weights: List[float], keywords: str, meeting_scenario: str, group_size: int, user_requirements: str, time_preference: str) -> ToolResult:
        """使用原生API执行推荐"""
        if hasattr(config._config, "amap") and hasattr(config._config.amap, "api_key"):
            self.api_key = config._config.amap.api_key
        
        if not self.api_key:
            logger.error("高德地图API密钥未配置。请在config.yml中设置 amap.api_key。")
            return ToolResult(output="推荐失败: 高德地图API密钥未配置。")

        try:
            coordinates = []
            location_info = []
            for location in locations:
                geocode_result = await self._geocode(location)
                if not geocode_result:
                    return ToolResult(output=f"无法找到地点: {location}")
                lng, lat = geocode_result["location"].split(",")
                coordinates.append((float(lng), float(lat)))
                location_info.append({
                    "name": location,
                    "formatted_address": geocode_result.get("formatted_address", location),
                    "location": geocode_result["location"],
                    "lng": float(lng),
                    "lat": float(lat)
                })

            if not coordinates:
                return ToolResult(output="未能解析任何有效地点。")

            center_point = self._calculate_center_point(coordinates, participant_weights)
            
            searched_places = await self._search_pois(
                f"{center_point[0]},{center_point[1]}",
                keywords, 
                radius=5000,
                types=""  # 使用空字符串作为默认值
            )

            if not searched_places:
                logger.info(f"使用 keywords '{keywords}' 未找到结果，尝试扩大搜索范围。")
                searched_places = await self._search_pois(
                    f"{center_point[0]},{center_point[1]}",
                    keywords,
                    radius=5000,
                    types="" 
                )
                if not searched_places:
                     return ToolResult(output=f"在计算的中心点附近找不到与 '{keywords}' 相关的场所。")

            # 集成实时数据
            logger.info("🌤️ 正在获取实时数据...")
            
            # 获取天气信息
            city_name = self._extract_city_name(locations)
            weather_info = await realtime_service.get_weather_info(city_name)
            
            # 获取交通分析
            traffic_analysis = await realtime_service.get_traffic_analysis(locations, center_point)
            
            # 获取营业状态信息
            searched_places = await realtime_service.get_business_hours_info(searched_places)

            recommended_places = self._rank_places(searched_places, center_point, user_requirements, keywords, meeting_scenario, group_size, weather_info, traffic_analysis)

            html_path = await self._generate_html_page(
                location_info,
                recommended_places,
                center_point,
                user_requirements,
                keywords,
                weather_info,
                traffic_analysis
            )
            result_text = self._format_result_text(location_info, recommended_places, html_path, keywords) 
            return ToolResult(output=result_text)

        except Exception as e:
            logger.exception(f"场所推荐过程中发生错误: {str(e)}") 
            return ToolResult(output=f"推荐失败: {str(e)}")

    async def _geocode(self, address: str) -> Optional[Dict[str, Any]]:
        if address in self.geocode_cache:
            return self.geocode_cache[address]
        url = "https://restapi.amap.com/v3/geocode/geo"
        params = {"key": self.api_key, "address": address, "output": "json"}
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                if response.status != 200:
                    logger.error(f"高德地图API地理编码请求失败: {response.status}, 地址: {address}")
                    return None
                data = await response.json()
                if data["status"] != "1" or not data["geocodes"]:
                    logger.error(f"地理编码失败: {data.get('info', '未知错误')}, 地址: {address}")
                    return None
                result = data["geocodes"][0]
                self.geocode_cache[address] = result
                return result

    def _calculate_center_point(self, coordinates: List[Tuple[float, float]], weights: List[float] = None) -> Tuple[float, float]:
        """计算加权中心点，支持多人会面场景优化"""
        if not coordinates:
            raise ValueError("至少需要一个坐标来计算中心点。")
        
        # 如果没有提供权重，使用相等权重
        if weights is None or len(weights) != len(coordinates):
            weights = [1.0] * len(coordinates)
        
        # 加权平均计算
        total_weight = sum(weights)
        weighted_lng = sum(lng * weight for (lng, _), weight in zip(coordinates, weights)) / total_weight
        weighted_lat = sum(lat * weight for (_, lat), weight in zip(coordinates, weights)) / total_weight
        
        # 多人会面优化：如果人数超过3人，考虑最小化总距离方差
        if len(coordinates) >= 3:
            # 使用几何中位数优化（简化版）
            return self._optimize_meetpoint(coordinates, weights, (weighted_lng, weighted_lat))
        
        return (weighted_lng, weighted_lat)
    
    def _optimize_meetpoint(self, coordinates: List[Tuple[float, float]], weights: List[float], initial_center: Tuple[float, float]) -> Tuple[float, float]:
        """优化会面点位置，最小化加权总距离"""
        center_lng, center_lat = initial_center
        
        # 简化的迭代优化算法
        for _ in range(5):  # 限制迭代次数避免过度计算
            total_weight = 0
            new_lng = 0
            new_lat = 0
            
            for (lng, lat), weight in zip(coordinates, weights):
                distance = self._calculate_distance((center_lng, center_lat), (lng, lat))
                if distance > 0:  # 避免除零
                    factor = weight / distance
                    total_weight += factor
                    new_lng += lng * factor
                    new_lat += lat * factor
            
            if total_weight > 0:
                center_lng = new_lng / total_weight
                center_lat = new_lat / total_weight
        
        return (center_lng, center_lat)
    
    def _extract_city_name(self, locations: List[Dict]) -> str:
        """从位置信息中提取城市名称"""
        for location_info in locations:
            address = location_info.get("formatted_address", "")
            if address:
                # 提取城市名称（简化处理）
                if "北京" in address:
                    return "北京"
                elif "上海" in address:
                    return "上海" 
                elif "深圳" in address:
                    return "深圳"
                elif "广州" in address:
                    return "广州"
                elif "杭州" in address:
                    return "杭州"
                # 更多城市可以继续添加
        
        # 默认返回北京
        return "北京"

    async def _search_pois(
        self,
        location: str,
        keywords: str,
        radius: int = 2000,
        types: str = "", 
        offset: int = 20
    ) -> List[Dict]:
        cache_key = f"{location}_{keywords}_{radius}_{types}"
        if cache_key in self.poi_cache:
            return self.poi_cache[cache_key]
        url = "https://restapi.amap.com/v3/place/around"
        params = {
            "key": self.api_key,
            "location": location,
            "keywords": keywords,
            "radius": radius,
            "offset": offset,
            "page": 1,
            "extensions": "all"
        }
        if types: 
            params["types"] = types

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                if response.status != 200:
                    logger.error(f"高德地图POI搜索失败: {response.status}, 参数: {params}")
                    return []
                data = await response.json()
                if data["status"] != "1":
                    logger.error(f"POI搜索API返回错误: {data.get('info', '未知错误')}, 参数: {params}")
                    return []
                pois = data.get("pois", [])
                self.poi_cache[cache_key] = pois
                return pois

    def _rank_places(
        self,
        places: List[Dict], 
        center_point: Tuple[float, float],
        user_requirements: str,
        keywords: str,
        meeting_scenario: str = "朋友聚会",
        group_size: int = 2,
        weather_info: Dict = None,
        traffic_analysis: Dict = None,
        personalized_weights: Dict = None
    ) -> List[Dict]:
        # 场景特定的评分权重
        scenario_weights = {
            "商务会议": {"安静": 15, "商务": 20, "包间": 15, "wifi": 10},
            "朋友聚会": {"氛围": 15, "性价比": 10, "停车": 10},
            "家庭聚餐": {"包间": 20, "停车": 15, "儿童": 10},
            "约会": {"氛围": 20, "安静": 15, "环境": 15},
            "运动健身": {"设施": 20, "停车": 10, "交通": 10},
            "其他": {}
        }
        
        # 群组大小权重调整
        group_size_factor = min(1.5, 1.0 + (group_size - 2) * 0.1)  # 人数越多权重越高
        
        # 获取个性化权重，如果没有则使用默认权重
        if personalized_weights is None:
            personalized_weights = {
                "distance": 1.0, "rating": 1.0, "venue_match": 1.0, "scenario_match": 1.0,
                "business_status": 1.0, "weather_suitability": 1.0, "traffic_condition": 1.0, "group_size_fit": 1.0
            }
        
        requirement_keywords_map = {
            "停车": ["停车", "车位", "停车场"],
            "安静": ["安静", "环境好", "氛围", "私密"],
            "商务": ["商务", "会议", "办公", "商业"],
            "交通": ["交通", "地铁", "公交", "便利"],
            "包间": ["包间", "包厢", "私人", "独立"],
            "wifi": ["wifi", "网络", "办公"],
            "氛围": ["氛围", "情调", "环境", "装修"],
            "性价比": ["实惠", "便宜", "性价比"],
            "儿童": ["儿童", "亲子", "家庭"],
            "设施": ["设施", "器材", "专业"]
        }
        
        user_priorities = []
        # 基于用户要求的优先级
        for key, kw_list in requirement_keywords_map.items():
            if any(kw in user_requirements for kw in kw_list):
                user_priorities.append(key)
        
        # 添加场景特定优先级
        current_scenario_weights = scenario_weights.get(meeting_scenario, {})

        for place in places:
            score = 0
            
            # 基础评分：评级（应用个性化权重）
            rating = float(place.get("biz_ext", {}).get("rating", "0") or "0") 
            score += rating * 15 * personalized_weights.get("rating", 1.0)
            
            # 位置评分：距离（应用个性化权重）
            place_lng_str, place_lat_str = place.get("location", "").split(",")
            if not place_lng_str or not place_lat_str: 
                place["_score"] = score 
                continue

            place_lng, place_lat = float(place_lng_str), float(place_lat_str)
            distance = self._calculate_distance(center_point, (place_lng, place_lat))
            distance_score = max(0, 25 * (1 - (distance / 3000)))  # 扩大距离考虑范围
            score += distance_score * group_size_factor * personalized_weights.get("distance", 1.0)
            
            # 记录距离信息用于学习
            place["_distance_from_center"] = distance
            
            # 群组大小容量评分
            if group_size > 4:
                if any(tag in place.get("tag", "") for tag in ["大厅", "大桌", "包间", "宴会"]):
                    score += 20
                elif "小" in place.get("tag", "") or "迷你" in place.get("tag", ""):
                    score -= 10  # 惩罚小空间
            
            # 用户要求评分
            place_tags = place.get("tag", "").lower()
            for priority in user_priorities:
                keywords_for_priority = requirement_keywords_map.get(priority, [])
                if any(kw in place_tags for kw in keywords_for_priority):
                    score += 12
            
            # 场景特定评分
            for feature, weight in current_scenario_weights.items():
                feature_keywords = requirement_keywords_map.get(feature, [])
                if any(kw in place_tags for kw in feature_keywords):
                    score += weight
            
            # 营业状态评分（如果有数据，应用个性化权重）
            business_status = place.get("business_status", "")
            if business_status == "营业":
                score += 15 * personalized_weights.get("business_status", 1.0)
            elif business_status == "暂停营业":
                score -= 50 * personalized_weights.get("business_status", 1.0)  # 严重降分
            
            # 天气适宜性评分
            if weather_info:
                weather_suitability = weather_info.get("suitable_for_meetup", {})
                weather_score = weather_suitability.get("score", 50)
                
                # 室内外场所的天气敏感性不同（应用个性化权重）
                weather_weight = personalized_weights.get("weather_suitability", 1.0)
                if any(tag in place.get("tag", "").lower() for tag in ["室外", "露天", "花园", "天台"]):
                    # 室外场所对天气更敏感
                    score += (weather_score - 50) * 0.4 * weather_weight
                else:
                    # 室内场所对天气不太敏感
                    score += (weather_score - 50) * 0.1 * weather_weight
            
            # 交通状况评分（应用个性化权重）
            if traffic_analysis:
                traffic_summary = traffic_analysis.get("summary", {})
                traffic_level = traffic_summary.get("traffic_level", "畅通")
                traffic_weight = personalized_weights.get("traffic_condition", 1.0)
                
                if traffic_level == "畅通":
                    score += 10 * traffic_weight
                elif traffic_level == "缓慢":
                    score += 5 * traffic_weight
                elif traffic_level == "拥堵":
                    score -= 5 * traffic_weight
                elif traffic_level == "严重拥堵":
                    score -= 15 * traffic_weight
                
            place["_score"] = score
            
            # 添加实时数据标记
            place["_has_realtime_data"] = True
            place["_weather_info"] = weather_info
            place["_traffic_info"] = traffic_analysis
        
        ranked_places = sorted(places, key=lambda x: x.get("_score", 0), reverse=True)
        return ranked_places[:5]


    def _calculate_distance(
        self,
        point1: Tuple[float, float],
        point2: Tuple[float, float]
    ) -> float:
        lng1, lat1 = point1
        lng2, lat2 = point2
        x = (lng2 - lng1) * 85000 
        y = (lat2 - lat1) * 111000 
        return math.sqrt(x*x + y*y)

    async def _generate_html_page(
        self,
        locations: List[Dict],
        places: List[Dict], 
        center_point: Tuple[float, float],
        user_requirements: str,
        keywords: str,
        weather_info: Dict = None,
        traffic_analysis: Dict = None,
        personalized_recommendations: List[str] = None,
        smart_suggestions: List[str] = None
    ) -> str:
        file_name_prefix = "place"
        
        html_content = self._generate_html_content(locations, places, center_point, user_requirements, keywords, weather_info, traffic_analysis, personalized_recommendations, smart_suggestions)
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        file_name = f"{file_name_prefix}_recommendation_{timestamp}_{unique_id}.html"
        
        workspace_js_src_path = os.path.join("workspace", "js_src")
        os.makedirs(workspace_js_src_path, exist_ok=True)
        file_path = os.path.join(workspace_js_src_path, file_name)

        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            await f.write(html_content)
        return file_path 

    def _generate_html_content(
        self,
        locations: List[Dict],
        places: List[Dict], 
        center_point: Tuple[float, float],
        user_requirements: str,
        keywords: str,
        weather_info: Dict = None,
        traffic_analysis: Dict = None,
        personalized_recommendations: List[str] = None,
        smart_suggestions: List[str] = None
    ) -> str:
        primary_keyword = keywords.split("、")[0] if keywords else "场所"
        cfg = self._get_place_config(primary_keyword)

        search_process_html = self._generate_search_process(locations, center_point, user_requirements, keywords) 

        location_markers = []
        for idx, loc in enumerate(locations):
            location_markers.append({
                "name": f"地点{idx+1}: {loc['name']}",
                "position": [loc["lng"], loc["lat"]],
                "icon": "location"
            })

        place_markers = [] 
        for place in places:
            lng_str, lat_str = place.get("location", ",").split(",")
            if lng_str and lat_str:
                place_markers.append({
                    "name": place["name"],
                    "position": [float(lng_str), float(lat_str)],
                    "icon": "place" 
                })

        center_marker = {
            "name": "最佳会面点",
            "position": [center_point[0], center_point[1]],
            "icon": "center"
        }
        all_markers = [center_marker] + location_markers + place_markers

        location_rows_html = ""
        for idx, loc in enumerate(locations):
            location_rows_html += f"<tr><td>{idx+1}</td><td>{loc['name']}</td><td>{loc['formatted_address']}</td></tr>"

        location_distance_html = ""
        for loc in locations:
            distance = self._calculate_distance(center_point, (loc['lng'], loc['lat']))/1000
            location_distance_html += f"<li><i class='bx bx-map'></i><strong>{loc['name']}</strong>: 距离中心点约 <span class='distance'>{distance:.1f} 公里</span></li>"

        place_cards_html = "" 
        for place in places:
            rating = place.get("biz_ext", {}).get("rating", "暂无评分")
            address = place.get("address", "地址未知")
            business_hours = place.get("business_hours", "营业时间未知")
            if isinstance(business_hours, list) and business_hours:
                business_hours = "; ".join(business_hours)
            tel = place.get("tel", "电话未知")
            
            tags = place.get("tag", [])
            if isinstance(tags, str): tags = tags.split(";") if tags else []
            elif not isinstance(tags, list): tags = []
            
            tags_html = "".join([f"<span class='cafe-tag'>{tg.strip()}</span>" for tg in tags if tg.strip()])
            if not tags_html: 
                tags_html = f"<span class='cafe-tag'>{cfg['noun_singular']}</span>"

            lng_str, lat_str = place.get("location",",").split(",")
            distance_text = "未知距离"
            map_link_coords = ""
            if lng_str and lat_str:
                lng, lat = float(lng_str), float(lat_str)
                distance = self._calculate_distance(center_point, (lng, lat))
                distance_text = f"{distance/1000:.1f} 公里"
                map_link_coords = f"{lng},{lat}"

            place_cards_html += f'''
            <div class="cafe-card"> 
                <div class="cafe-img">
                    <i class='bx {cfg["icon_card"]}'></i> 
                </div>
                <div class="cafe-content">
                    <div class="cafe-header">
                        <div>
                            <h3 class="cafe-name">{place['name']}</h3>
                        </div>
                        <span class="cafe-rating">评分: {rating}</span>
                    </div>
                    <div class="cafe-details">
                        <div class="cafe-info">
                            <i class='bx bx-map'></i>
                            <div class="cafe-info-text">{address}</div>
                        </div>
                        <div class="cafe-info">
                            <i class='bx bx-time'></i>
                            <div class="cafe-info-text">{business_hours}</div>
                        </div>
                        <div class="cafe-info">
                            <i class='bx bx-phone'></i>
                            <div class="cafe-info-text">{tel}</div>
                        </div>
                        <div class="cafe-tags">
                            {tags_html}
                        </div>
                    </div>
                    <div class="cafe-footer">
                        <div class="cafe-distance">
                            <i class='bx bx-walk'></i> {distance_text}
                        </div>
                        <div class="cafe-actions">
                            <a href="https://uri.amap.com/marker?position={map_link_coords}&name={place['name']}" target="_blank">
                                <i class='bx bx-navigation'></i>导航
                            </a>
                        </div>
                    </div>
                </div>
            </div>'''
        markers_json = json.dumps(all_markers)

        amap_security_js_code = ""
        if hasattr(config, 'amap') and hasattr(config.amap, 'security_js_code') and config.amap.security_js_code:
            amap_security_js_code = config.amap.security_js_code

        # Dynamically set CSS variables for theme colors
        dynamic_style = f"""
        :root {{
            --primary: {cfg.get("theme_primary", "#9c6644")}; 
            --primary-light: {cfg.get("theme_primary_light", "#c68b59")};
            --primary-dark: {cfg.get("theme_primary_dark", "#7f5539")};
            --secondary: {cfg.get("theme_secondary", "#c9ada7")};
            --light: {cfg.get("theme_light", "#f2e9e4")};
            --dark: {cfg.get("theme_dark", "#22223b")};
            --success: #4a934a; /* Success color can remain static or be themed */
            --border-radius: 12px;
            --box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            --transition: all 0.3s ease;
        }}"""

        html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{cfg["topic"]} - 最佳会面{cfg["noun_singular"]}推荐</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/boxicons@2.0.9/css/boxicons.min.css">
    <style>
        {dynamic_style} /* Inject dynamic theme colors here */

        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{ font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; line-height: 1.6; background-color: var(--light); color: var(--dark); padding-bottom: 50px; }}
        .container {{ max-width: 1200px; margin: 0 auto; padding: 0 20px; }}
        header {{ background-color: var(--primary); color: white; padding: 60px 0 100px; text-align: center; position: relative; margin-bottom: 80px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); }}
        header::after {{ content: ''; position: absolute; bottom: 0; left: 0; right: 0; height: 60px; background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 60"><path fill="{cfg.get("theme_light", "#f2e9e4")}" fill-opacity="1" d="M0,32L80,42.7C160,53,320,75,480,64C640,53,800,11,960,5.3C1120,0,1280,32,1360,48L1440,64L1440,100L1360,100C1280,100,1120,100,960,100C800,100,640,100,480,100C320,100,160,100,80,100L0,100Z"></path></svg>'); background-size: cover; background-position: center; }}
        .header-logo {{ font-size: 3rem; font-weight: 700; margin-bottom: 10px; letter-spacing: -1px; }}
        .coffee-icon {{ font-size: 3rem; vertical-align: middle; margin-right: 10px; }}
        .header-subtitle {{ font-size: 1.2rem; opacity: 0.9; }}
        .main-content {{ margin-top: -60px; }}
        .card {{ background-color: white; border-radius: var(--border-radius); padding: 30px; box-shadow: var(--box-shadow); margin-bottom: 30px; transition: var(--transition); }}
        .card:hover {{ transform: translateY(-5px); box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1); }}
        .section-title {{ font-size: 1.8rem; color: var(--primary-dark); margin-bottom: 25px; padding-bottom: 15px; border-bottom: 2px solid var(--secondary); display: flex; align-items: center; }}
        .section-title i {{ margin-right: 12px; font-size: 1.6rem; color: var(--primary); }}
        .summary-card {{ display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 15px; }}
        .summary-item {{ flex: 1; min-width: 200px; padding: 15px; background-color: rgba(0,0,0,0.03); /* Adjusted for better contrast with various themes */ border-radius: 8px; border-left: 4px solid var(--primary); }}
        .summary-label {{ font-size: 0.9rem; color: var(--primary-dark); margin-bottom: 5px; }}
        .summary-value {{ font-size: 1.2rem; font-weight: 600; color: var(--dark); }}
        .map-container {{ height: 500px; border-radius: var(--border-radius); overflow: hidden; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); position: relative; margin-bottom: 30px; }}
        #map {{ height: 100%; width: 100%; }}
        .map-legend {{ position: absolute; bottom: 15px; left: 15px; background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.15); z-index: 100; }}
        .legend-item {{ display: flex; align-items: center; margin-bottom: 8px; }}
        .legend-color {{ width: 20px; height: 20px; margin-right: 10px; border-radius: 50%; }}
        .legend-center {{ background-color: #2ecc71; }} 
        .legend-location {{ background-color: #3498db; }} 
        .legend-place {{ background-color: #e74c3c; }} 
        .location-table {{ width: 100%; border-collapse: collapse; border-radius: 8px; overflow: hidden; margin-bottom: 25px; box-shadow: 0 0 8px rgba(0, 0, 0, 0.1); }}
        .location-table th, .location-table td {{ padding: 15px; text-align: left; border-bottom: 1px solid #eee; }}
        .location-table th {{ background-color: var(--primary-light); color: white; font-weight: 600; }}
        .location-table tr:last-child td {{ border-bottom: none; }}
        .location-table tr:nth-child(even) {{ background-color: rgba(0,0,0,0.02); /* Adjusted for better contrast */ }}
        .cafe-grid {{ display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 25px; margin-top: 20px; }} 
        .cafe-card {{ background-color: white; border-radius: 12px; overflow: hidden; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08); transition: var(--transition); display: flex; flex-direction: column; }}
        .cafe-card:hover {{ transform: translateY(-10px); box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15); }}
        .cafe-img {{ height: 180px; background-color: var(--primary-light); display: flex; align-items: center; justify-content: center; color: white; font-size: 3rem; }}
        .cafe-content {{ padding: 20px; flex: 1; display: flex; flex-direction: column; }}
        .cafe-header {{ display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 15px; }}
        .cafe-name {{ font-size: 1.3rem; margin: 0 0 5px 0; color: var(--primary-dark); }}
        .cafe-rating {{ display: inline-block; background-color: var(--primary); color: white; padding: 5px 12px; border-radius: 20px; font-weight: 600; font-size: 0.9rem; white-space: nowrap; }}
        .cafe-details {{ flex: 1; }}
        .cafe-info {{ margin-bottom: 12px; display: flex; align-items: flex-start; }}
        .cafe-info i {{ color: var(--primary); margin-right: 8px; font-size: 1.1rem; min-width: 20px; margin-top: 3px; }}
        .cafe-info-text {{ flex: 1; }}
        .cafe-tags {{ display: flex; flex-wrap: wrap; gap: 6px; margin-top: 15px; }}
        .cafe-tag {{ background-color: rgba(0,0,0,0.05); /* Adjusted for better contrast */ color: var(--primary-dark); padding: 4px 10px; border-radius: 15px; font-size: 0.8rem; }}
        .cafe-footer {{ display: flex; align-items: center; justify-content: space-between; margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee; }}
        .cafe-distance {{ display: flex; align-items: center; color: var(--primary-dark); font-weight: 600; }}
        .cafe-distance i {{ margin-right: 5px; }}
        .cafe-actions a {{ display: inline-flex; align-items: center; justify-content: center; background-color: var(--primary); color: white; padding: 8px 15px; border-radius: 6px; text-decoration: none; font-size: 0.9rem; transition: var(--transition); }}
        .cafe-actions a:hover {{ background-color: var(--primary-dark); transform: translateY(-2px); }}
        .cafe-actions i {{ margin-right: 5px; }}
        .transportation-info {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px; margin-top: 20px; }}
        .transport-card {{ background-color: white; border-radius: 12px; padding: 25px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); border-top: 5px solid var(--primary); }}
        .transport-title {{ font-size: 1.3rem; color: var(--primary-dark); margin-bottom: 15px; display: flex; align-items: center; }}
        .transport-title i {{ margin-right: 10px; font-size: 1.4rem; color: var(--primary); }}
        .transport-list {{ list-style: none; margin: 0; padding: 0; }}
        .transport-list li {{ padding: 10px 0; border-bottom: 1px solid #eee; display: flex; align-items: center; }}
        .transport-list li:last-child {{ border-bottom: none; }}
        .transport-list i {{ color: var(--primary); margin-right: 10px; }}
        .center-coords {{ display: inline-block; background-color: rgba(0,0,0,0.05); /* Adjusted for better contrast */ border-radius: 6px; padding: 3px 8px; margin: 0 5px; font-family: monospace; font-size: 0.9rem; }}
        .footer {{ text-align: center; background-color: var(--primary-dark); color: white; padding: 20px 0; margin-top: 50px; }}
        .back-button {{ display: inline-flex; align-items: center; justify-content: center; background-color: white; color: var(--primary); border: 2px solid var(--primary); padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; font-size: 1rem; transition: var(--transition); margin-top: 30px; }}
        .back-button:hover {{ background-color: var(--primary); color: white; transform: translateY(-3px); box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); }}
        .back-button i {{ margin-right: 8px; }}
        .search-process-card {{ position: relative; overflow: hidden; background-color: #fafafa; border-left: 5px solid #2c3e50; }} /* Search process card can have static border */
        .search-process {{ position: relative; padding: 20px 0; }}
        .process-step {{ display: flex; margin-bottom: 30px; opacity: 0.5; transform: translateX(-20px); transition: opacity 0.5s ease, transform 0.5s ease; }}
        .process-step.active {{ opacity: 1; transform: translateX(0); }}
        .step-icon {{ flex: 0 0 60px; height: 60px; border-radius: 50%; background-color: var(--primary-light); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem; margin-right: 20px; position: relative; }}
        .step-number {{ position: absolute; top: -5px; right: -5px; width: 25px; height: 25px; border-radius: 50%; background-color: var(--primary-dark); color: white; display: flex; align-items: center; justify-content: center; font-size: 0.8rem; font-weight: bold; }}
        .step-content {{ flex: 1; }}
        .step-title {{ font-size: 1.3rem; color: var(--primary-dark); margin-bottom: 10px; }}
        .step-details {{ background-color: white; border-radius: 10px; padding: 15px; box-shadow: 0 3px 10px rgba(0,0,0,0.05); }}
        .code-block {{ background-color: #2c3e50; color: #e6e6e6; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 0.9rem; margin: 15px 0; white-space: pre; overflow-x: auto; }}
        .highlight-text {{ background-color: rgba(46, 204, 113, 0.2); color: #2c3e50; padding: 3px 6px; border-radius: 4px; font-weight: bold; }}
        .search-animation {{ height: 200px; position: relative; display: flex; align-items: center; justify-content: center; margin: 20px 0; }}
        .radar-circle {{ position: absolute; width: 50px; height: 50px; border-radius: 50%; background-color: rgba(52, 152, 219, 0.1); animation: radar 3s infinite; }}
        .radar-circle:nth-child(1) {{ animation-delay: 0s; }} .radar-circle:nth-child(2) {{ animation-delay: 1s; }} .radar-circle:nth-child(3) {{ animation-delay: 2s; }}
        .center-point {{ width: 15px; height: 15px; border-radius: 50%; background-color: #e74c3c; z-index: 2; box-shadow: 0 0 0 5px rgba(231, 76, 60, 0.3); }}
        .map-operation-animation {{ height: 200px; position: relative; border-radius: 8px; overflow: hidden; background-color: #f5f5f5; margin: 20px 0; box-shadow: 0 3px 10px rgba(0,0,0,0.1); }}
        .map-bg {{ position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23f0f0f0"/><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="%23ccc" stroke-width="0.5"/><path d="M50,0 L50,100 M0,50 L100,50" stroke="%23ccc" stroke-width="0.5"/></svg>'); background-size: 50px 50px; opacity: 0.7; }}
        .map-cursor {{ position: absolute; width: 20px; height: 20px; background-color: rgba(231, 76, 60, 0.7); border-radius: 50%; top: 50%; left: 30%; transform: translate(-50%, -50%); animation: mapCursor 4s infinite ease-in-out; z-index: 2; }}
        .map-search-indicator {{ position: absolute; width: 80px; height: 80px; border: 2px dashed rgba(52, 152, 219, 0.6); border-radius: 50%; top: 50%; left: 50%; transform: translate(-50%, -50%); animation: mapSearch 3s infinite ease-in-out; z-index: 1; }}
        @keyframes mapCursor {{ 0% {{ left: 30%; top: 30%; }} 30% {{ left: 60%; top: 40%; }} 60% {{ left: 40%; top: 70%; }} 100% {{ left: 30%; top: 30%; }} }}
        @keyframes mapSearch {{ 0% {{ width: 30px; height: 30px; opacity: 1; }} 100% {{ width: 150px; height: 150px; opacity: 0; }} }}
        @keyframes radar {{ 0% {{ width: 40px; height: 40px; opacity: 1; }} 100% {{ width: 300px; height: 300px; opacity: 0; }} }}
        .ranking-result {{ margin-top: 15px; }}
        .result-bar {{ height: 30px; background-color: var(--primary); color: white; margin-bottom: 8px; border-radius: 15px; padding: 0 15px; display: flex; align-items: center; font-weight: 600; box-shadow: 0 2px 5px rgba(0,0,0,0.1); animation: growBar 2s ease; transform-origin: left; }}
        @keyframes growBar {{ 0% {{ width: 0; }} 100% {{ width: 100%; }} }}
        .mt-4 {{ margin-top: 1rem; }}
        @media (max-width: 768px) {{ .cafe-grid {{ grid-template-columns: 1fr; }} .transportation-info {{ grid-template-columns: 1fr; }} header {{ padding: 40px 0 80px; }} .header-logo {{ font-size: 2.2rem; }} .process-step {{ flex-direction: column; }} .step-icon {{ margin-bottom: 15px; margin-right: 0; }} }}
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-logo">
                <i class='bx {cfg["icon_header"]} coffee-icon'></i>{cfg["topic"]}
            </div>
            <div class="header-subtitle">为您找到的最佳会面{cfg["noun_plural"]}</div>
        </div>
    </header>

    <div class="container main-content">
        <div class="card">
            <h2 class="section-title"><i class='bx bx-info-circle'></i>推荐摘要</h2>
            <div class="summary-card">
                <div class="summary-item">
                    <div class="summary-label">参与地点数</div>
                    <div class="summary-value">{len(locations)} 个地点</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">推荐{cfg["noun_plural"]}数</div>
                    <div class="summary-value">{len(places)} 家{cfg["noun_plural"]}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">特殊需求</div>
                    <div class="summary-value">{user_requirements or "无特殊需求"}</div>
                </div>
                {self._generate_realtime_summary_html(weather_info, traffic_analysis)}
            </div>
        </div>
        
        {self._generate_realtime_info_html(weather_info, traffic_analysis)}
        {self._generate_personalized_info_html(personalized_recommendations, smart_suggestions)}
        {search_process_html}
        <div class="card">
            <h2 class="section-title"><i class='bx bx-map-pin'></i>地点信息</h2>
            <table class="location-table">
                <thead><tr><th>序号</th><th>地点名称</th><th>详细地址</th></tr></thead>
                <tbody>{location_rows_html}</tbody>
            </table>
        </div>
        <div class="card">
            <h2 class="section-title"><i class='bx bx-map-alt'></i>地图展示</h2>
            <div class="map-container">
                <div id="map"></div>
                <div class="map-legend">
                    <div class="legend-item"><div class="legend-color legend-center"></div><span>最佳会面点</span></div>
                    <div class="legend-item"><div class="legend-color legend-location"></div><span>所在地点</span></div>
                    <div class="legend-item"><div class="legend-color legend-place"></div><span>{cfg["map_legend"]}</span></div>
                </div>
            </div>
        </div>
        <div class="card">
            <h2 class="section-title"><i class='bx {cfg["icon_section"]}'></i>推荐{cfg["noun_plural"]}</h2>
            <div class="cafe-grid">
                {place_cards_html}
            </div>
        </div>
        <div class="card">
            <h2 class="section-title"><i class='bx bx-car'></i>交通与停车建议</h2>
            <div class="transportation-info">
                <div class="transport-card">
                    <h3 class="transport-title"><i class='bx bx-trip'></i>前往方式</h3>
                    <p>最佳会面点位于<span class="center-coords">{center_point[0]:.6f}, {center_point[1]:.6f}</span>附近</p>
                    <ul class="transport-list">{location_distance_html}</ul>
                </div>
                <div class="transport-card">
                    <h3 class="transport-title"><i class='bx bxs-car-garage'></i>停车建议</h3>
                    <ul class="transport-list">
                        <li><i class='bx bx-check'></i>大部分推荐的{cfg["noun_plural"]}周边有停车场或提供停车服务</li>
                        <li><i class='bx bx-check'></i>建议使用高德地图或百度地图导航到目的地</li>
                        <li><i class='bx bx-check'></i>高峰时段建议提前30分钟出发，以便寻找停车位</li>
                        <li><i class='bx bx-check'></i>部分{cfg["noun_plural"]}可能提供免费停车或停车优惠</li>
                    </ul>
                </div>
            </div>
            <a href="/workspace/meetspot_finder.html" class="back-button"> 
                <i class='bx bx-left-arrow-alt'></i>返回首页
            </a>
        </div>
    </div>
    <footer class="footer">
        <div class="container">
            <p>© {datetime.now().year} {cfg["topic"]} - 智能{cfg["noun_singular"]}推荐服务 | 数据来源：高德地图</p>
        </div>
    </footer>
    <script type="text/javascript">
        var markersData = {markers_json};
        window._AMapSecurityConfig = {{ securityJsCode: "{amap_security_js_code}" }};
        window.onload = function() {{
            var script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = 'https://webapi.amap.com/loader.js';
            script.onload = function() {{
                AMapLoader.load({{
                    key: "{self.api_key}", 
                    version: "2.0",
                    plugins: ["AMap.Scale", "AMap.ToolBar"],
                    AMapUI: {{ version: "1.1", plugins: ["overlay/SimpleMarker"] }}
                }})
                .then(function(AMap) {{ initMap(AMap); }})
                .catch(function(e) {{ console.error('地图加载失败:', e); }});
            }};
            document.body.appendChild(script);
            animateCafeCards(); 
        }};
        function initMap(AMap) {{
            var map = new AMap.Map('map', {{
                zoom: 12, center: [{center_point[0]}, {center_point[1]}],
                resizeEnable: true, viewMode: '3D'
            }});
            map.addControl(new AMap.ToolBar()); map.addControl(new AMap.Scale());
            var mapMarkers = []; 
            markersData.forEach(function(item) {{
                var markerContent, position = new AMap.LngLat(item.position[0], item.position[1]);
                var color = '#e74c3c'; 
                if (item.icon === 'center') color = '#2ecc71'; 
                else if (item.icon === 'location') color = '#3498db'; 
                
                markerContent = `<div style="background-color: ${{color}}; width: 24px; height: 24px; border-radius: 12px; border: 2px solid white; box-shadow: 0 0 5px rgba(0,0,0,0.3);"></div>`;
                
                var marker = new AMap.Marker({{
                    position: position, content: markerContent,
                    title: item.name, anchor: 'center', offset: new AMap.Pixel(0, 0)
                }});
                var infoWindow = new AMap.InfoWindow({{
                    content: '<div style="padding:10px;font-size:14px;">' + item.name + '</div>',
                    offset: new AMap.Pixel(0, -12)
                }});
                marker.on('click', function() {{ infoWindow.open(map, marker.getPosition()); }});
                mapMarkers.push(marker);
                marker.setMap(map);
            }});
            if (markersData.length > 1) {{
                var pathCoordinates = [];
                markersData.filter(item => item.icon !== 'place').forEach(function(item) {{ 
                    pathCoordinates.push(new AMap.LngLat(item.position[0], item.position[1]));
                }});
                if (pathCoordinates.length > 1) {{ 
                    var polyline = new AMap.Polyline({{
                        path: pathCoordinates, strokeColor: '#3498db', strokeWeight: 4,
                        strokeStyle: 'dashed', strokeDasharray: [5, 5], lineJoin: 'round'
                    }});
                    polyline.setMap(map);
                }}
            }}
            if (mapMarkers.length > 0) {{ 
                 map.setFitView(mapMarkers);
            }}
        }}
        function animateCafeCards() {{
            const cards = document.querySelectorAll('.cafe-card');
            if ('IntersectionObserver' in window) {{
                const observer = new IntersectionObserver((entries) => {{
                    entries.forEach(entry => {{
                        if (entry.isIntersecting) {{
                            entry.target.style.opacity = 1;
                            entry.target.style.transform = 'translateY(0)';
                            observer.unobserve(entry.target);
                        }}
                    }});
                }}, {{ threshold: 0.1 }});
                cards.forEach((card, index) => {{
                    card.style.opacity = 0; card.style.transform = 'translateY(30px)';
                    card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    card.style.transitionDelay = (index * 0.1) + 's';
                    observer.observe(card);
                }});
            }} else {{
                cards.forEach((card, index) => {{
                    card.style.opacity = 0; card.style.transform = 'translateY(30px)';
                    card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    setTimeout(() => {{ card.style.opacity = 1; card.style.transform = 'translateY(0)'; }}, 300 + (index * 100));
                }});
            }}
        }}
    </script>
</body>
</html>"""
        return html
    
    def _generate_realtime_summary_html(self, weather_info: Dict = None, traffic_analysis: Dict = None) -> str:
        """生成实时数据摘要HTML"""
        summary_items = []
        
        if weather_info:
            weather_level = weather_info.get("suitable_for_meetup", {}).get("level", "一般")
            weather_icon = "🌤️" if weather_level == "适宜" else "🌧️" if weather_level == "不适宜" else "☁️"
            summary_items.append(f'''
                <div class="summary-item">
                    <div class="summary-label">{weather_icon} 天气状况</div>
                    <div class="summary-value">{weather_info.get("weather", "晴")} {weather_info.get("temperature", "22")}°C</div>
                </div>
            ''')
        
        if traffic_analysis:
            traffic_level = traffic_analysis.get("summary", {}).get("traffic_level", "畅通")
            traffic_icon = "🚗" if traffic_level == "畅通" else "🚙" if traffic_level == "缓慢" else "🚨"
            avg_time = traffic_analysis.get("summary", {}).get("average_travel_time", "30分钟")
            summary_items.append(f'''
                <div class="summary-item">
                    <div class="summary-label">{traffic_icon} 交通状况</div>
                    <div class="summary-value">{traffic_level} (平均{avg_time})</div>
                </div>
            ''')
        
        return "".join(summary_items)
    
    def _generate_realtime_info_html(self, weather_info: Dict = None, traffic_analysis: Dict = None) -> str:
        """生成详细实时信息HTML"""
        if not weather_info and not traffic_analysis:
            return ""
        
        html_sections = []
        
        if weather_info:
            weather_suitability = weather_info.get("suitable_for_meetup", {})
            recommendations = weather_suitability.get("recommendations", [])
            recommendations_html = "".join([f"<li><i class='bx bx-check'></i>{rec}</li>" for rec in recommendations]) if recommendations else "<li><i class='bx bx-check'></i>天气适宜出行</li>"
            
            weather_html = f'''
            <div class="card">
                <h2 class="section-title"><i class='bx bx-cloud'></i>实时天气信息</h2>
                <div class="transportation-info">
                    <div class="transport-card">
                        <h3 class="transport-title"><i class='bx bxs-thermometer'></i>当前天气</h3>
                        <div style="display: flex; align-items: center; gap: 20px; margin: 15px 0;">
                            <div style="font-size: 2rem;">{weather_info.get("weather", "晴")}</div>
                            <div>
                                <div style="font-size: 1.5rem; font-weight: bold;">{weather_info.get("temperature", "22")}°C</div>
                                <div style="color: #666;">湿度: {weather_info.get("humidity", "45")}%</div>
                            </div>
                        </div>
                        <div style="color: #666; font-size: 0.9rem;">数据更新: {weather_info.get("reportTime", "刚刚")}</div>
                    </div>
                    <div class="transport-card">
                        <h3 class="transport-title"><i class='bx bx-target-lock'></i>会面适宜性</h3>
                        <div style="margin: 15px 0;">
                            <div style="font-size: 1.2rem; font-weight: bold; color: {'#27ae60' if weather_suitability.get('level') == '适宜' else '#f39c12' if weather_suitability.get('level') == '一般' else '#e74c3c'};">
                                {weather_suitability.get("level", "一般")} (评分: {weather_suitability.get("score", 50)}/100)
                            </div>
                        </div>
                        <ul class="transport-list">{recommendations_html}</ul>
                    </div>
                </div>
            </div>
            '''
            html_sections.append(weather_html)
        
        if traffic_analysis:
            traffic_summary = traffic_analysis.get("summary", {})
            recommendations = traffic_summary.get("recommendations", [])
            recommendations_html = "".join([f"<li><i class='bx bx-check'></i>{rec}</li>" for rec in recommendations]) if recommendations else "<li><i class='bx bx-check'></i>交通状况良好</li>"
            
            routes_html = ""
            for i, route in enumerate(traffic_analysis.get("individual_routes", [])):
                routes_html += f'''
                <div style="padding: 10px; margin: 5px 0; background: #f8f9fa; border-radius: 6px;">
                    <strong>{route.get("origin", f"位置{i+1}")}</strong>
                    <span style="float: right; color: #666;">
                        🕐 {route.get("duration", 30)}分钟 | 📍 {route.get("distance", 10000)/1000:.1f}公里
                    </span>
                </div>
                '''
            
            traffic_html = f'''
            <div class="card">
                <h2 class="section-title"><i class='bx bx-car'></i>实时交通分析</h2>
                <div class="transportation-info">
                    <div class="transport-card">
                        <h3 class="transport-title"><i class='bx bx-traffic-cone'></i>路况概况</h3>
                        <div style="margin: 15px 0;">
                            <div style="font-size: 1.2rem; font-weight: bold; color: {'#27ae60' if traffic_summary.get('traffic_level') == '畅通' else '#f39c12' if traffic_summary.get('traffic_level') == '缓慢' else '#e74c3c'};">
                                {traffic_summary.get("traffic_level", "畅通")}
                            </div>
                            <div style="color: #666; margin: 5px 0;">
                                平均到达时间: {traffic_summary.get("average_travel_time", "30分钟")}
                            </div>
                            <div style="color: #666;">
                                总距离: {traffic_summary.get("total_distance", "15.0公里")}
                            </div>
                        </div>
                        <ul class="transport-list">{recommendations_html}</ul>
                    </div>
                    <div class="transport-card">
                        <h3 class="transport-title"><i class='bx bx-map-alt'></i>各位置详情</h3>
                        <div>{routes_html}</div>
                    </div>
                </div>
            </div>
            '''
            html_sections.append(traffic_html)
        
        return "".join(html_sections)
    
    def _generate_personalized_info_html(self, personalized_recommendations: List[str] = None, smart_suggestions: List[str] = None) -> str:
        """生成个性化推荐信息HTML"""
        if not personalized_recommendations and not smart_suggestions:
            return ""
        
        html_sections = []
        
        if personalized_recommendations:
            recommendations_html = "".join([f"<li><i class='bx bx-check-circle'></i>{rec}</li>" for rec in personalized_recommendations])
            
            personal_html = f'''
            <div class="card">
                <h2 class="section-title"><i class='bx bx-user-circle'></i>个性化推荐</h2>
                <div class="transportation-info">
                    <div class="transport-card">
                        <h3 class="transport-title"><i class='bx bx-brain'></i>基于您的偏好</h3>
                        <p style="color: #666; margin-bottom: 15px;">系统根据您的历史选择为您提供个性化推荐</p>
                        <ul class="transport-list">{recommendations_html}</ul>
                    </div>
                    {self._generate_suggestions_html(smart_suggestions)}
                </div>
            </div>
            '''
            html_sections.append(personal_html)
        elif smart_suggestions:
            # 只有智能建议时的显示
            suggestions_html = f'''
            <div class="card">
                <h2 class="section-title"><i class='bx bx-bulb'></i>智能建议</h2>
                <div class="transportation-info">
                    {self._generate_suggestions_html(smart_suggestions)}
                </div>
            </div>
            '''
            html_sections.append(suggestions_html)
        
        return "".join(html_sections)
    
    def _generate_suggestions_html(self, smart_suggestions: List[str] = None) -> str:
        """生成智能建议HTML"""
        if not smart_suggestions:
            return ""
        
        suggestions_html = "".join([f"<li><i class='bx bx-lightbulb'></i>{sug}</li>" for sug in smart_suggestions])
        
        return f'''
        <div class="transport-card">
            <h3 class="transport-title"><i class='bx bx-bulb'></i>智能建议</h3>
            <p style="color: #666; margin-bottom: 15px;">为您提供更好会面体验的贴心建议</p>
            <ul class="transport-list">{suggestions_html}</ul>
        </div>
        '''

    def _format_result_text(
        self,
        locations: List[Dict],
        places: List[Dict], 
        html_path: str,
        keywords: str,
        personalized_recommendations: List[str] = None
    ) -> str:
        primary_keyword = keywords.split("、")[0] if keywords else "场所"
        cfg = self._get_place_config(primary_keyword)
        num_places = len(places)

        result = [
            f"## 已为您找到{num_places}家适合会面的{cfg['noun_plural']}", 
            "",
        ]
        
        # 添加个性化推荐信息
        if personalized_recommendations and len(personalized_recommendations) > 0:
            result.extend([
                "### 🎯 个性化推荐理由:",
                ""
            ])
            for i, recommendation in enumerate(personalized_recommendations, 1):
                result.append(f"{i}. {recommendation}")
            result.append("")
        
        result.append(f"### 推荐{cfg['noun_plural']}:")
        
        for i, place in enumerate(places):
            rating = place.get("biz_ext", {}).get("rating", "暂无评分")
            address = place.get("address", "地址未知")
            business_status = place.get("business_status", "营业状态未知")
            distance = place.get("_distance_from_center", 0)
            
            # 格式化距离显示
            if distance > 0:
                if distance < 1000:
                    distance_str = f"{distance:.0f}米"
                else:
                    distance_str = f"{distance/1000:.1f}公里"
            else:
                distance_str = "距离未知"
            
            result.append(f"{i+1}. **{place['name']}** (评分: {rating})")
            result.append(f"   📍 地址: {address}")
            result.append(f"   🚗 距离: {distance_str} | 🏪 营业状态: {business_status}")
            result.append("")
        
        html_file_basename = os.path.basename(html_path)
        result.append(f"📄 HTML页面: {html_file_basename}") 
        result.append(f"可在浏览器中打开查看详细地图和{cfg['noun_plural']}信息。") 

        return "\n".join(result)

    def _generate_search_process(
        self,
        locations: List[Dict],
        center_point: Tuple[float, float],
        user_requirements: str,
        keywords: str 
    ) -> str:
        primary_keyword = keywords.split("、")[0] if keywords else "场所"
        cfg = self._get_place_config(primary_keyword)
        search_steps = []

        location_analysis = "<ul>"
        for idx, loc in enumerate(locations):
            location_analysis += f"<li>分析位置 {idx+1}: <strong>{loc['name']}</strong></li>"
        location_analysis += "</ul>"
        search_steps.append({
            "icon": "bx-map-pin", "title": "分析用户位置信息",
            "content": f"<p>我检测到{len(locations)}个不同的位置。正在分析它们的地理分布...</p>{location_analysis}"
        })

        search_steps.append({
            "icon": "bx-map", "title": f"正在操作高德地图寻找最佳{cfg['noun_singular']}的位置...", 
            "content": f"""
            <p>正在操作高德地图寻找最佳{cfg['noun_singular']}的位置...</p> 
            <div class="map-operation-animation">
                <div class="map-bg"></div> <div class="map-cursor"></div> <div class="map-search-indicator"></div>
            </div>"""
        })

        requirement_analysis = ""
        if user_requirements:
            requirement_keywords_map = {
                "停车": ["停车", "车位", "停车场"], "安静": ["安静", "环境好", "氛围"],
                "商务": ["商务", "会议", "办公"], "交通": ["交通", "地铁", "公交"]
            }
            detected_requirements = [key for key, kw_list in requirement_keywords_map.items() if any(kw in user_requirements for kw in kw_list)]
            if detected_requirements:
                requirement_analysis = "<p>我从您的需求中检测到以下关键偏好:</p><ul>" + "".join([f"<li><strong>{req}</strong>: 将优先考虑{req}便利的{cfg['noun_plural']}</li>" for req in detected_requirements]) + "</ul>" 
            else:
                requirement_analysis = f"<p>您没有提供特定的需求偏好，将基于综合评分和距离推荐最佳{cfg['noun_plural']}。</p>" 
        else:
            requirement_analysis = f"<p>未提供特殊需求，将根据评分和位置便利性进行推荐{cfg['noun_plural']}。</p>" 
        search_steps.append({"icon": "bx-list-check", "title": "分析用户特殊需求", "content": requirement_analysis})

        search_places_explanation = f"""
        <p>我正在以最佳会面点为中心，搜索周边2公里范围内的{cfg['noun_plural']}...</p> 
        <div class="search-animation">
            <div class="radar-circle"></div> <div class="radar-circle"></div> <div class="radar-circle"></div>
            <div class="center-point"></div>
        </div>"""
        search_steps.append({"icon": "bx-search-alt", "title": f"搜索周边{cfg['noun_plural']}", "content": search_places_explanation}) 

        ranking_explanation = f"""
        <p>我已找到多家{cfg['noun_plural']}，正在根据综合评分对它们进行排名...</p> 
        <div class="ranking-result">
            <div class="result-bar" style="width: 95%;">{cfg['noun_singular']}评分</div> 
            <div class="result-bar" style="width: 85%;">距离便利性</div>
            <div class="result-bar" style="width: 75%;">环境舒适度</div>
            <div class="result-bar" style="width: 65%;">交通便利性</div>
        </div>"""
        search_steps.append({"icon": "bx-sort", "title": f"对{cfg['noun_plural']}进行排名", "content": ranking_explanation}) 

        search_process_html = ""
        for idx, step in enumerate(search_steps):
            search_process_html += f"""
            <div class="process-step" data-step="{idx+1}">
                <div class="step-icon"><i class='bx {step["icon"]}'></i><div class="step-number">{idx+1}</div></div>
                <div class="step-content"><h3 class="step-title">{step["title"]}</h3><div class="step-details">{step["content"]}</div></div>
            </div>"""

        search_process_javascript = """
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            const steps = document.querySelectorAll('.process-step');
            let currentStep = 0;
            function showNextStep() {
                if (currentStep < steps.length) {
                    steps[currentStep].classList.add('active');
                    currentStep++;
                    setTimeout(showNextStep, 1500); 
                }
            }
            setTimeout(showNextStep, 500); 
        });
        </script>"""
        return f"""
        <div class="card search-process-card">
            <h2 class="section-title"><i class='bx bx-bot'></i>AI 搜索过程</h2>
            <div class="search-process">{search_process_html}</div>
            {search_process_javascript}
        </div>"""

