/* recommendations.wxss */
.header {
  background-color: var(--primary-dark);
  color: white;
  padding: 40rpx 30rpx;
  margin: -30rpx -20rpx 30rpx;
  border-bottom-left-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
}

.section-title {
  font-size: 36rpx;
  color: var(--primary-dark);
  margin: 40rpx 0 20rpx;
  font-weight: bold;
}

.summary, .location-info, .transport-card {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 30rpx;
  box-shadow: var(--box-shadow);
  margin-bottom: 30rpx;
}

.summary-requirements {
  margin-top: 20rpx;
}

.label {
  font-weight: bold;
  margin-right: 10rpx;
}

.location-table {
  margin-top: 20rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  overflow: hidden;
}

.table-header {
  display: flex;
  background-color: #f5f5f5;
  font-weight: bold;
}

.table-row {
  display: flex;
  border-top: 1rpx solid #eee;
}

.table-cell {
  padding: 20rpx;
  flex: 1;
}

.map-container {
  height: 500rpx;
  margin-bottom: 30rpx;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.places-container {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-bottom: 30rpx;
}

.place-card {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 30rpx;
  box-shadow: var(--box-shadow);
}

.place-name {
  font-size: 34rpx;
  font-weight: bold;
  color: var(--primary-dark);
  margin-bottom: 20rpx;
}

.place-info {
  margin-bottom: 20rpx;
}

.info-item {
  margin-bottom: 10rpx;
  font-size: 28rpx;
}

.place-tags {
  margin-top: 20rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.place-tag {
  background-color: var(--light);
  color: var(--primary);
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.place-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.nav-btn, .call-btn {
  flex: 1;
  font-size: 28rpx;
  padding: 16rpx 0;
  border-radius: 8rpx;
  text-align: center;
}

.nav-btn {
  background-color: var(--primary);
  color: white;
}

.call-btn {
  background-color: var(--light);
  color: var(--primary);
}

.transport-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-dark);
  margin: 20rpx 0 10rpx;
}

.transport-list {
  margin: 10rpx 0 20rpx;
}

.transport-item {
  margin-bottom: 10rpx;
  font-size: 28rpx;
}

.footer {
  text-align: center;
  margin-top: 50rpx;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
} 