# 高德地图API配置
[amap]
api_key = "041db813f69a2424f234fade1e3b3605"
web_api_key = "041db813f69a2424f234fade1e3b3605"

# MCP服务配置
[mcp]
# 高德地图MCP服务 - ModelScope托管
amap_sse_url = "https://mcp.api-inference.modelscope.net/9b0135aaa8d748/sse"
use_official_mcp = false  # 优先使用ModelScope的MCP服务

# 基本LLM配置 - 硅基流动
[llm]
model = "Qwen/Qwen2.5-72B-Instruct"
base_url = "https://api.siliconflow.cn/v1"
api_key = "sk-kjqbzjjtrwfvjwnwjtovqjfzrqrwvngtdzoeleijemmajovb"
max_tokens = 4096
temperature = 0.7
api_type = "Openai"
api_version = ""

# Vision LLM配置 - 硅基流动
[llm.vision]
model = "Qwen/Qwen2.5-72B-Instruct"
base_url = "https://api.siliconflow.cn/v1"
api_key = "sk-kjqbzjjtrwfvjwnwjtovqjfzrqrwvngtdzoeleijemmajovb"
max_tokens = 4096
temperature = 0.7
api_type = "Openai"
api_version = ""

# 沙箱配置
[sandbox]
use_sandbox = false
image = "python:3.12-slim"
work_dir = "/workspace"
memory_limit = "512m"
cpu_limit = 1.0
timeout = 300
network_enabled = false

# 浏览器配置
[browser]
headless = false
disable_security = true
max_content_length = 2000

# 搜索配置
[search]
engine = "Google"

# 服务器配置
[server]
host = "127.0.0.1"
port = 8000