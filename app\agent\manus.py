#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Manus Agent for MeetSpot application
"""

import asyncio
import re
from typing import Dict, List, Optional, Any
from app.logger import logger
from app.tool.meetspot_recommender import CafeRecommender


class Manus:
    """Manus agent for processing meeting point recommendations"""
    
    def __init__(self):
        self.recommender = CafeRecommender()
    
    async def run(self, user_query: str) -> str:
        """
        Process user query and return recommendation results
        """
        try:
            logger.info(f"Processing user query: {user_query}")
            
            # Parse user query to extract locations and preferences
            parsed_data = self._parse_query(user_query)
            locations = parsed_data.get("locations", [])
            keywords = parsed_data.get("keywords", "咖啡馆")
            requirements = parsed_data.get("requirements", "")
            meeting_scenario = parsed_data.get("meeting_scenario", "朋友聚会")
            group_size = parsed_data.get("group_size", len(locations) if locations else 2)
            
            if not locations:
                return "❌ 无法识别位置信息，请提供至少一个地点。"
            
            logger.info(f"多人会面推荐 - 位置数量: {len(locations)}, 场景: {meeting_scenario}, 人数: {group_size}")
            
            # Execute recommendation with enhanced parameters
            result = await self.recommender.execute(
                locations=locations,
                keywords=keywords,
                meeting_scenario=meeting_scenario,
                group_size=group_size,
                user_requirements=requirements
            )
            
            return result.output
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return f"❌ 处理查询时出错: {str(e)}"
    
    def _parse_query(self, query: str) -> Dict[str, Any]:
        """
        Parse user query to extract locations, keywords, requirements, and scenario
        """
        locations = []
        keywords = "咖啡馆"
        requirements = ""
        meeting_scenario = "朋友聚会"
        group_size = 2
        
        # Enhanced location patterns for multiple participants
        location_patterns = [
            r'在(.+?)(?:、|,|，)(.+?)(?:、|,|，)?(.+?)(?:之间|中间|附近)',  # 支持3个地点
            r'在(.+?)(?:和|与|及)(.+?)(?:之间|中间|附近)',
            r'从(.+?)到(.+?)(?:的|之间)',
            r'(.+?)(?:、|,|，)(.+?)(?:、|,|，)?(.+?)(?:会面|见面|聚会)',  # 支持多人聚会
            r'(.+?)(?:和|与|及)(.+?)(?:会面|见面|聚会)',
        ]
        
        for pattern in location_patterns:
            match = re.search(pattern, query)
            if match:
                locations.extend([loc.strip() for loc in match.groups() if loc and loc.strip()])
                break
        
        # If no pattern match, try to extract individual locations
        if not locations:
            # Look for common location keywords
            location_keywords = ['市', '区', '路', '街', '站', '广场', '商场', '大厦', '中心', '园', '城']
            words = query.replace('，', ' ').replace(',', ' ').split()
            for word in words:
                if any(keyword in word for keyword in location_keywords) and len(word) > 2:
                    locations.append(word)
        
        # 推断群组大小
        if len(locations) > 2:
            group_size = len(locations)
        
        # 检测人数关键词
        import re
        numbers_match = re.search(r'(\d+)(?:人|个人)', query)
        if numbers_match:
            group_size = int(numbers_match.group(1))
        
        # Extract keywords for venue type
        venue_keywords = {
            '咖啡': '咖啡馆',
            '餐厅': '餐厅',
            '饭店': '餐厅',
            '图书馆': '图书馆',
            '书店': '图书馆',
            '商场': '商场',
            '购物': '商场',
            '公园': '公园',
            '电影院': '电影院',
            '影院': '电影院',
            '篮球': '篮球场',
            '运动': '篮球场'
        }
        
        for keyword, venue_type in venue_keywords.items():
            if keyword in query:
                keywords = venue_type
                break
        
        # 检测会面场景
        scenario_keywords = {
            '商务': '商务会议',
            '会议': '商务会议',
            '工作': '商务会议',
            '办公': '商务会议',
            '朋友': '朋友聚会',
            '聚会': '朋友聚会',
            '同学': '朋友聚会',
            '家庭': '家庭聚餐',
            '家人': '家庭聚餐',
            '聚餐': '家庭聚餐',
            '约会': '约会',
            '情侣': '约会',
            '运动': '运动健身',
            '健身': '运动健身',
            '篮球': '运动健身'
        }
        
        for keyword, scenario in scenario_keywords.items():
            if keyword in query:
                meeting_scenario = scenario
                break
        
        # Extract requirements
        requirement_patterns = [
            r'(?:需要|要求|希望).+?(?:停车|安静|商务|交通|包间|wifi)',
            r'(?:停车|安静|商务|交通|包间|wifi).+?(?:方便|便利|好)'
        ]
        
        for pattern in requirement_patterns:
            match = re.search(pattern, query)
            if match:
                requirements = match.group(0)
                break
        
        logger.info(f"Enhanced parsing - Locations: {locations}, Keywords: {keywords}, Scenario: {meeting_scenario}, Group Size: {group_size}, Requirements: {requirements}")
        
        return {
            "locations": locations,
            "keywords": keywords,
            "requirements": requirements,
            "meeting_scenario": meeting_scenario,
            "group_size": group_size
        }