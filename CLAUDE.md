# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

MeetSpot is a meeting point recommendation system that won the MCP&Agent Challenge Track 2. It's built with Python/Gradio for the web interface and includes a WeChat Mini Program version. The system uses Amap (高德地图) API through MCP (Model Context Protocol) to provide intelligent meeting location recommendations.

## Key Components

### Core Architecture
- **MCP Client System**: `app/mcp/` - Handles Amap API integration via MCP protocol
- **Recommendation Engine**: `app/tool/meetspot_recommender.py` - Core algorithm for finding optimal meeting points
- **Gradio Web Interface**: `app_modelscope.py` - Main web application
- **Agent System**: `app/agent/manus.py` - Intelligent query processing agent
- **WeChat Mini Program**: `miniprogram/` - Mobile app version
- **Configuration**: `config/config.toml` - API keys and service configuration
- **Base Classes**: `app/tool/base.py` - Tool foundation classes
- **Logging**: `app/logger.py` - Centralized logging system

### MCP Integration
The system uses ModelScope hosted MCP service:
- **Primary**: ModelScope MCP service (SSE protocol)
- **Fallback**: Official Amap MCP service (requires API key)
- **Degradation**: Native Amap API (requires API key)

The MCP client (`app/mcp/amap_mcp_client.py`) handles both official and ModelScope endpoints with automatic fallback and supports all core Amap services including geocoding, POI search, routing, and weather.

## Development Commands

### Running the Application

**Main Application (ModelScope/Gradio)**:
```bash
python app_modelscope.py
```

**Launch Script (Alternative)**:
```bash
python launch.py
```

**Web Server Version**:
```bash
python web_server.py
```

### Testing

**功能测试**:
```bash
# 完整功能测试
python test_functionality.py
```

**Essential Tests**:
```bash
# MCP integration tests
python test_mcp_integration.py
python test_official_mcp.py

# Full system tests
python test_full_mcp.py
python test_dual_mcp_competition.py
```

**Quick Tests**:
```bash
# Simple MCP tests
python quick_mcp_test.py
python simple_mcp_test.py
python simple_official_test.py
```

### Configuration

1. **MCP服务配置**: 在`config/config.toml`中配置MCP服务
   ```toml
   [mcp]
   # 高德地图MCP服务 - ModelScope托管
   amap_sse_url = "https://mcp.api-inference.modelscope.net/9b0135aaa8d748/sse"
   use_official_mcp = false  # 优先使用ModelScope的MCP服务
   ```

2. **API密钥配置**: 配置在`config/config.toml`中
   - `amap.api_key` - 高德地图API密钥（作为MCP服务的备用）
   - `llm.api_key` - LLM API密钥用于智能推荐

3. **服务端点**: 系统自动选择MCP服务端点
   - 优先使用: ModelScope托管的MCP服务 (SSE)
   - 备用: 官方高德地图MCP服务 (需要API密钥)
   - 降级: 原生高德地图API (需要API密钥)

## Architecture Patterns

### MCP Service Pattern
- Uses async context managers for connection management
- Implements automatic fallback between official and ModelScope endpoints
- Supports both JSON-RPC and simplified SSE protocols

### Recommendation Algorithm
The `CafeRecommender` class in `app/tool/meetspot_recommender.py` implements:
1. **Geocoding**: Convert addresses to coordinates
2. **Center Point Calculation**: Find optimal meeting location
3. **POI Search**: Find nearby venues using Amap API
4. **Ranking Algorithm**: Score venues based on distance, rating, and user preferences
5. **HTML Generation**: Create interactive maps and recommendations

### Theme System
The recommender supports multiple venue types with custom themes:
- Coffee shops, restaurants, libraries, shopping malls, parks, cinemas, basketball courts
- Each type has unique icons, colors, and display configurations

### Error Handling
- Graceful degradation when MCP services are unavailable
- Automatic retry logic for API calls
- Fallback to basic recommendations when external services fail

## File Structure

```
app/
├── __init__.py             # Package initialization with create_app
├── agent/                  # Agent system
│   ├── __init__.py
│   └── manus.py           # Query processing agent
├── mcp/                    # MCP client implementations
│   ├── __init__.py
│   ├── amap_mcp_client.py  # Main MCP client
│   ├── amap_mcp_client_sse.py # SSE-specific client
│   └── railway_mcp_client.py # Railway-specific client
├── tool/                   # Core recommendation logic
│   ├── __init__.py
│   ├── base.py            # Base tool classes
│   └── meetspot_recommender.py # Main recommendation engine
├── config.py              # Configuration management
└── logger.py              # Logging system
config/
└── config.toml            # Configuration file
miniprogram/               # WeChat Mini Program
├── app.js                # Mini program entry point
├── pages/                # Mini program pages
workspace/                # Generated HTML files and assets
test_*.py                 # Essential test files
app_modelscope.py         # Main Gradio application
launch.py                 # Alternative launcher
web_server.py             # FastAPI web server
```

## Key Dependencies

- **gradio**: Web interface framework
- **fastapi**: Web API framework (for web_server.py)
- **aiohttp**: Async HTTP client for MCP communication
- **pydantic**: Data validation and settings management
- **aiofiles**: Async file operations
- **toml**: Configuration file parsing
- **uvicorn**: ASGI server for FastAPI

## Common Development Tasks

### Adding New Venue Types
1. Add configuration to `PLACE_TYPE_CONFIG` in `meetspot_recommender.py`
2. Update theme colors and icons
3. Test with different search keywords

### Modifying Recommendation Logic
- Edit the `_rank_places` method in `CafeRecommender`
- Adjust scoring weights for distance, rating, and user preferences
- Test with available test files

### Testing MCP Integration
- Use `test_mcp_integration.py` for basic connectivity
- Use `test_full_mcp.py` for complete workflow testing
- Use `test_official_mcp.py` for official MCP service testing
- Check logs in `logs/` directory for debugging

## Deployment Notes

- **ModelScope**: Use `app_modelscope.py` as entry point
- **Local Development**: Use `launch.py` or run `app_modelscope.py` directly
- **Port**: Default is 7860 for ModelScope compatibility
- **Environment**: Configure API keys in `config/config.toml` before deployment