<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MeetSpot (聚点) - 智能会面点推荐</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            padding: 30px 0;
        }

        .header h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .form-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            padding: 40px;
            margin-bottom: 30px;
        }

        .section {
            margin-bottom: 35px;
        }

        .section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.4rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section h3 i {
            color: #667eea;
            font-size: 1.6rem;
        }

        .locations-container {
            margin-bottom: 20px;
        }

        .location-input {
            margin-bottom: 15px;
            position: relative;
        }

        .location-input input {
            width: 100%;
            padding: 15px 20px 15px 50px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .location-input input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .location-input i {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
            font-size: 1.2rem;
        }

        .add-location-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .add-location-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .place-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .place-type {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
        }

        .place-type:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .place-type.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .place-type i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }

        .place-type span {
            font-weight: 600;
            font-size: 1rem;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .filter-group {
            background: #f8f9ff;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e0e6ff;
        }

        .filter-group label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
            font-size: 0.95rem;
        }

        .filter-group input, .filter-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .filter-group input:focus, .filter-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .custom-keywords {
            margin-top: 20px;
        }

        .custom-keywords input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .custom-keywords input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .requirements-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 1rem;
            resize: vertical;
            min-height: 100px;
            transition: all 0.3s ease;
        }

        .requirements-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .submit-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 18px 40px;
            border-radius: 15px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .submit-btn:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }

        .submit-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
            color: #667eea;
        }

        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .suggestions {
            background: #f0f4ff;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }

        .suggestions h4 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .suggestions h4 i {
            color: #667eea;
        }

        .suggestion-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .suggestion-tag {
            background: #667eea;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .suggestion-tag:hover {
            background: #764ba2;
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2.2rem;
            }

            .form-card {
                padding: 25px;
            }

            .place-types {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class='bx bxs-map'></i> MeetSpot 聚点</h1>
            <p>智能会面点推荐系统 - 让每次聚会都找到完美地点</p>
        </div>

        <div class="form-card">
            <form id="meetspotForm">
                <!-- 参与者地点 -->
                <div class="section">
                    <h3><i class='bx bxs-map-pin'></i>参与者地点</h3>
                    <div class="locations-container" id="locationsContainer">
                        <div class="location-input">
                            <i class='bx bxs-location-plus'></i>
                            <input type="text" name="location" placeholder="请输入第一个地点，如：北京朝阳区望京" required>
                        </div>
                        <div class="location-input">
                            <i class='bx bxs-location-plus'></i>
                            <input type="text" name="location" placeholder="请输入第二个地点，如：海淀区中关村" required>
                        </div>
                    </div>
                    <button type="button" class="add-location-btn" onclick="addLocation()">
                        <i class='bx bx-plus'></i>添加更多地点
                    </button>
                </div>

                <!-- 场所类型选择 -->
                <div class="section">
                    <h3><i class='bx bxs-store'></i>选择场景类型</h3>
                    <div class="place-types">
                        <div class="place-type" data-type="咖啡馆">
                            <i class='bx bxs-coffee-togo'></i>
                            <span>咖啡馆</span>
                        </div>
                        <div class="place-type" data-type="餐厅">
                            <i class='bx bxs-restaurant'></i>
                            <span>餐厅</span>
                        </div>
                        <div class="place-type" data-type="图书馆">
                            <i class='bx bxs-book'></i>
                            <span>图书馆</span>
                        </div>
                        <div class="place-type" data-type="商场">
                            <i class='bx bxs-shopping-bag'></i>
                            <span>商场</span>
                        </div>
                        <div class="place-type" data-type="公园">
                            <i class='bx bxs-tree'></i>
                            <span>公园</span>
                        </div>
                        <div class="place-type" data-type="电影院">
                            <i class='bx bxs-film'></i>
                            <span>电影院</span>
                        </div>
                        <div class="place-type" data-type="健身房">
                            <i class='bx bxs-dumbbell'></i>
                            <span>健身房</span>
                        </div>
                        <div class="place-type" data-type="KTV">
                            <i class='bx bxs-microphone'></i>
                            <span>KTV</span>
                        </div>
                        <div class="place-type" data-type="博物馆">
                            <i class='bx bxs-institution'></i>
                            <span>博物馆</span>
                        </div>
                        <div class="place-type" data-type="景点">
                            <i class='bx bxs-landmark'></i>
                            <span>景点</span>
                        </div>
                        <div class="place-type" data-type="购物中心">
                            <i class='bx bxs-shopping-bags'></i>
                            <span>购物中心</span>
                        </div>
                        <div class="place-type" data-type="游戏厅">
                            <i class='bx bxs-joystick'></i>
                            <span>游戏厅</span>
                        </div>
                        <div class="place-type" data-type="书店">
                            <i class='bx bxs-book-open'></i>
                            <span>书店</span>
                        </div>
                        <div class="place-type" data-type="酒吧">
                            <i class='bx bxs-drink'></i>
                            <span>酒吧</span>
                        </div>
                        <div class="place-type" data-type="茶楼">
                            <i class='bx bxs-coffee-bean'></i>
                            <span>茶楼</span>
                        </div>
                    </div>

                    <div class="custom-keywords">
                        <input type="text" id="customKeywords" placeholder="或者输入自定义关键词，如：游泳馆、瑜伽馆、密室逃脱...">
                    </div>
                </div>

                <!-- 过滤条件 -->
                <div class="section">
                    <h3><i class='bx bxs-filter-alt'></i>筛选条件</h3>
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label for="minRating">最低评分</label>
                            <select id="minRating">
                                <option value="0">不限制</option>
                                <option value="3.0">3.0分以上</option>
                                <option value="3.5">3.5分以上</option>
                                <option value="4.0">4.0分以上</option>
                                <option value="4.5">4.5分以上</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="maxDistance">最大距离</label>
                            <select id="maxDistance">
                                <option value="10000">不限制</option>
                                <option value="1000">1公里内</option>
                                <option value="2000">2公里内</option>
                                <option value="3000">3公里内</option>
                                <option value="5000">5公里内</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="priceRange">价格区间</label>
                            <select id="priceRange">
                                <option value="">不限制</option>
                                <option value="economy">经济实惠</option>
                                <option value="mid">中等消费</option>
                                <option value="high">高端消费</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="limitNum">推荐数量</label>
                            <select id="limitNum">
                                <option value="10">10个</option>
                                <option value="15">15个</option>
                                <option value="20" selected>20个</option>
                                <option value="30">30个</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 特殊需求 -->
                <div class="section">
                    <h3><i class='bx bxs-star'></i>特殊需求</h3>
                    <textarea 
                        id="userRequirements" 
                        class="requirements-input" 
                        placeholder="请描述您的特殊需求，例如：&#10;• 停车方便&#10;• 环境安静&#10;• 适合商务洽谈&#10;• 有Wi-Fi&#10;• 适合儿童&#10;• 可以聊天到很晚&#10;• 有包间"
                    ></textarea>

                    <div class="suggestions">
                        <h4><i class='bx bx-bulb'></i>常用需求建议</h4>
                        <div class="suggestion-tags">
                            <span class="suggestion-tag" onclick="addRequirement('停车方便')">停车方便</span>
                            <span class="suggestion-tag" onclick="addRequirement('环境安静')">环境安静</span>
                            <span class="suggestion-tag" onclick="addRequirement('有Wi-Fi')">有Wi-Fi</span>
                            <span class="suggestion-tag" onclick="addRequirement('适合商务')">适合商务</span>
                            <span class="suggestion-tag" onclick="addRequirement('适合儿童')">适合儿童</span>
                            <span class="suggestion-tag" onclick="addRequirement('有包间')">有包间</span>
                            <span class="suggestion-tag" onclick="addRequirement('可以久坐')">可以久坐</span>
                            <span class="suggestion-tag" onclick="addRequirement('24小时营业')">24小时营业</span>
                        </div>
                    </div>
                </div>

                <button type="submit" class="submit-btn" id="submitBtn">
                    <i class='bx bx-search-alt-2'></i>
                    查找最佳会面点
                </button>
            </form>

            <div class="loading" id="loading">
                <i class='bx bx-loader-alt'></i>
                <p>正在为您寻找最佳会面点...</p>
            </div>
        </div>
    </div>

    <script>
        let selectedPlaceTypes = new Set();

        // 场所类型选择
        document.querySelectorAll('.place-type').forEach(type => {
            type.addEventListener('click', function() {
                const typeValue = this.getAttribute('data-type');
                
                if (this.classList.contains('selected')) {
                    this.classList.remove('selected');
                    selectedPlaceTypes.delete(typeValue);
                } else {
                    this.classList.add('selected');
                    selectedPlaceTypes.add(typeValue);
                }
            });
        });

        // 添加地点输入框
        function addLocation() {
            const container = document.getElementById('locationsContainer');
            const locationInputs = container.querySelectorAll('.location-input');
            
            if (locationInputs.length >= 8) {
                alert('最多支持8个地点');
                return;
            }

            const newLocationDiv = document.createElement('div');
            newLocationDiv.className = 'location-input';
            newLocationDiv.innerHTML = `
                <i class='bx bxs-location-plus'></i>
                <input type="text" name="location" placeholder="请输入地点">
                <button type="button" onclick="removeLocation(this)" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #ff6b6b; font-size: 1.2rem; cursor: pointer;">
                    <i class='bx bx-x'></i>
                </button>
            `;
            container.appendChild(newLocationDiv);
        }

        // 删除地点输入框
        function removeLocation(button) {
            const locationInput = button.closest('.location-input');
            const container = document.getElementById('locationsContainer');
            
            if (container.querySelectorAll('.location-input').length > 2) {
                locationInput.remove();
            } else {
                alert('至少需要保留两个地点');
            }
        }

        // 添加需求标签
        function addRequirement(requirement) {
            const textarea = document.getElementById('userRequirements');
            const currentValue = textarea.value.trim();
            
            if (currentValue && !currentValue.endsWith('\n')) {
                textarea.value = currentValue + '\n• ' + requirement;
            } else {
                textarea.value = currentValue + '• ' + requirement;
            }
        }

        // 表单提交
        document.getElementById('meetspotForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitBtn = document.getElementById('submitBtn');
            const loading = document.getElementById('loading');

            // 收集地点数据
            const locationInputs = document.querySelectorAll('input[name="location"]');
            const locations = Array.from(locationInputs)
                .map(input => input.value.trim())
                .filter(location => location);

            if (locations.length < 2) {
                alert('请至少输入两个地点');
                return;
            }

            // 收集关键词
            let keywords = '';
            const customKeywords = document.getElementById('customKeywords').value.trim();
            
            if (customKeywords) {
                keywords = customKeywords;
            } else if (selectedPlaceTypes.size > 0) {
                keywords = Array.from(selectedPlaceTypes).join(' ');
            } else {
                keywords = '咖啡馆'; // 默认值
            }

            // 收集过滤条件
            const minRating = parseFloat(document.getElementById('minRating').value) || 0;
            const maxDistance = parseInt(document.getElementById('maxDistance').value) || 10000;
            const priceRange = document.getElementById('priceRange').value || '';
            const limit = parseInt(document.getElementById('limitNum').value) || 20;
            const userRequirements = document.getElementById('userRequirements').value.trim();

            // 显示加载状态
            submitBtn.disabled = true;
            loading.style.display = 'block';

            try {
                const response = await fetch('/api/find_meetspot', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        locations: locations,
                        keywords: keywords,
                        user_requirements: userRequirements,
                        min_rating: minRating,
                        max_distance: maxDistance,
                        price_range: priceRange,
                        limit: limit
                    })
                });

                const result = await response.json();

                if (result.success && result.html_url) {
                    // 在新标签页打开结果页面
                    window.open(result.html_url, '_blank');
                } else {
                    alert('推荐失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                console.error('请求失败:', error);
                alert('请求失败，请检查网络连接');
            } finally {
                submitBtn.disabled = false;
                loading.style.display = 'none';
            }
        });

        // 页面加载完成后默认选择咖啡馆
        document.addEventListener('DOMContentLoaded', function() {
            const cafeType = document.querySelector('.place-type[data-type="咖啡馆"]');
            if (cafeType) {
                cafeType.click();
            }
        });
    </script>
</body>
</html>
