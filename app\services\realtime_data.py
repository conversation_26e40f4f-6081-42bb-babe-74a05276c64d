#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时数据服务模块
集成天气、交通状况、营业状态等实时信息
"""

import asyncio
import json
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from app.logger import logger
from app.config import config


class RealTimeDataService:
    """实时数据服务，提供天气、交通、营业状态等信息"""
    
    def __init__(self):
        self.weather_cache = {}
        self.traffic_cache = {}
        self.cache_duration = timedelta(minutes=30)  # 缓存30分钟
    
    async def get_weather_info(self, city: str) -> Dict[str, Any]:
        """获取天气信息"""
        cache_key = f"weather_{city}"
        
        # 检查缓存
        if cache_key in self.weather_cache:
            cached_data, cached_time = self.weather_cache[cache_key]
            if datetime.now() - cached_time < self.cache_duration:
                return cached_data
        
        try:
            # 优先使用MCP服务
            from app.mcp.amap_mcp_client import AmapMCPService
            mcp_service = AmapMCPService(use_official=False)
            if await mcp_service.initialize():
                weather_result = await mcp_service.get_weather(city)
                
                if not weather_result.get("error"):
                    processed_weather = self._process_weather_data(weather_result)
                    self.weather_cache[cache_key] = (processed_weather, datetime.now())
                    return processed_weather
            
            # 回退到模拟数据
            return self._get_mock_weather(city)
            
        except Exception as e:
            logger.error(f"获取天气信息失败: {e}")
            return self._get_mock_weather(city)
    
    def _process_weather_data(self, weather_result: Dict) -> Dict[str, Any]:
        """处理天气数据"""
        try:
            live_data = weather_result.get("result", {}).get("lives", [{}])[0]
            return {
                "city": live_data.get("city", ""),
                "weather": live_data.get("weather", ""),
                "temperature": live_data.get("temperature", ""),
                "humidity": live_data.get("humidity", ""),
                "windDirection": live_data.get("winddirection", ""),
                "windPower": live_data.get("windpower", ""),
                "reportTime": live_data.get("reporttime", ""),
                "suitable_for_meetup": self._evaluate_weather_suitability(live_data)
            }
        except Exception as e:
            logger.error(f"处理天气数据失败: {e}")
            return self._get_mock_weather("")
    
    def _evaluate_weather_suitability(self, weather_data: Dict) -> Dict[str, Any]:
        """评估天气对会面的适宜性"""
        weather = weather_data.get("weather", "").lower()
        temp = int(weather_data.get("temperature", "20"))
        
        score = 100
        recommendations = []
        
        # 天气状况评分
        if any(condition in weather for condition in ["雨", "雪", "雷"]):
            score -= 30
            recommendations.append("建议选择室内场所")
        elif any(condition in weather for condition in ["阴", "多云"]):
            score -= 10
        
        # 温度评分
        if temp < 0 or temp > 35:
            score -= 20
            recommendations.append("注意保暖" if temp < 0 else "注意防暑")
        elif temp < 10 or temp > 30:
            score -= 10
        
        return {
            "score": max(0, score),
            "level": "适宜" if score >= 70 else "一般" if score >= 40 else "不适宜",
            "recommendations": recommendations
        }
    
    def _get_mock_weather(self, city: str) -> Dict[str, Any]:
        """模拟天气数据"""
        return {
            "city": city or "北京",
            "weather": "晴",
            "temperature": "22",
            "humidity": "45",
            "windDirection": "南",
            "windPower": "3级",
            "reportTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "suitable_for_meetup": {
                "score": 85,
                "level": "适宜",
                "recommendations": []
            }
        }
    
    async def get_traffic_analysis(self, locations: List[str], center_point: tuple) -> Dict[str, Any]:
        """分析到会面点的交通状况"""
        try:
            traffic_data = []
            total_time = 0
            total_distance = 0
            
            for location in locations:
                route_info = await self._get_route_info(location, f"{center_point[0]},{center_point[1]}")
                traffic_data.append(route_info)
                total_time += route_info.get("duration", 0)
                total_distance += route_info.get("distance", 0)
            
            avg_time = total_time / len(locations) if locations else 0
            
            return {
                "individual_routes": traffic_data,
                "summary": {
                    "average_travel_time": f"{avg_time:.0f}分钟",
                    "total_distance": f"{total_distance/1000:.1f}公里",
                    "traffic_level": self._evaluate_traffic_level(avg_time),
                    "recommendations": self._get_traffic_recommendations(avg_time)
                }
            }
            
        except Exception as e:
            logger.error(f"获取交通分析失败: {e}")
            return self._get_mock_traffic_analysis(locations)
    
    async def _get_route_info(self, origin: str, destination: str) -> Dict[str, Any]:
        """获取路线信息"""
        cache_key = f"route_{origin}_{destination}"
        
        # 检查缓存
        if cache_key in self.traffic_cache:
            cached_data, cached_time = self.traffic_cache[cache_key]
            if datetime.now() - cached_time < timedelta(minutes=15):  # 交通信息缓存15分钟
                return cached_data
        
        try:
            # 使用MCP服务获取路线
            from app.mcp.amap_mcp_client import AmapMCPService
            mcp_service = AmapMCPService(use_official=False)
            if await mcp_service.initialize():
                # 先地理编码
                origin_result = await mcp_service.geocode(origin)
                if not origin_result.get("error") and origin_result.get("result", {}).get("geocodes"):
                    origin_location = origin_result["result"]["geocodes"][0]["location"]
                    
                    # 获取路线
                    route_result = await mcp_service.driving_route(origin_location, destination)
                    if not route_result.get("error"):
                        processed_route = self._process_route_data(route_result, origin)
                        self.traffic_cache[cache_key] = (processed_route, datetime.now())
                        return processed_route
            
            # 回退到模拟数据
            return self._get_mock_route_info(origin)
            
        except Exception as e:
            logger.error(f"获取路线信息失败: {e}")
            return self._get_mock_route_info(origin)
    
    def _process_route_data(self, route_result: Dict, origin: str) -> Dict[str, Any]:
        """处理路线数据"""
        try:
            route_data = route_result.get("result", {}).get("route", {})
            paths = route_data.get("paths", [{}])
            
            if paths:
                path = paths[0]
                duration_seconds = int(path.get("duration", "0"))
                distance_meters = int(path.get("distance", "0"))
                
                return {
                    "origin": origin,
                    "duration": duration_seconds // 60,  # 转换为分钟
                    "distance": distance_meters,
                    "traffic_lights": len(path.get("steps", [])),
                    "tolls": path.get("tolls", 0),
                    "restriction": path.get("restriction", 0)
                }
        except Exception as e:
            logger.error(f"处理路线数据失败: {e}")
        
        return self._get_mock_route_info(origin)
    
    def _get_mock_route_info(self, origin: str) -> Dict[str, Any]:
        """模拟路线信息"""
        import random
        return {
            "origin": origin,
            "duration": random.randint(20, 60),  # 20-60分钟
            "distance": random.randint(5000, 25000),  # 5-25公里
            "traffic_lights": random.randint(5, 20),
            "tolls": 0,
            "restriction": 0
        }
    
    def _evaluate_traffic_level(self, avg_time: float) -> str:
        """评估交通拥堵级别"""
        if avg_time < 20:
            return "畅通"
        elif avg_time < 35:
            return "缓慢"
        elif avg_time < 50:
            return "拥堵"
        else:
            return "严重拥堵"
    
    def _get_traffic_recommendations(self, avg_time: float) -> List[str]:
        """获取交通建议"""
        recommendations = []
        
        if avg_time > 40:
            recommendations.extend([
                "建议错峰出行",
                "考虑选择公共交通",
                "提前30分钟出发"
            ])
        elif avg_time > 25:
            recommendations.extend([
                "建议提前15分钟出发",
                "关注实时路况"
            ])
        
        return recommendations
    
    def _get_mock_traffic_analysis(self, locations: List[str]) -> Dict[str, Any]:
        """模拟交通分析数据"""
        import random
        
        mock_routes = []
        for location in locations:
            mock_routes.append(self._get_mock_route_info(location))
        
        avg_time = sum(route["duration"] for route in mock_routes) / len(mock_routes) if mock_routes else 30
        
        return {
            "individual_routes": mock_routes,
            "summary": {
                "average_travel_time": f"{avg_time:.0f}分钟",
                "total_distance": f"{sum(route['distance'] for route in mock_routes)/1000:.1f}公里",
                "traffic_level": self._evaluate_traffic_level(avg_time),
                "recommendations": self._get_traffic_recommendations(avg_time)
            }
        }
    
    async def get_business_hours_info(self, places: List[Dict]) -> List[Dict]:
        """获取营业时间信息并判断当前状态"""
        enhanced_places = []
        current_time = datetime.now()
        current_hour = current_time.hour
        
        for place in places:
            enhanced_place = place.copy()
            
            # 模拟营业时间判断（实际项目中可通过POI详情API获取）
            business_status = self._determine_business_status(current_hour, place.get("tag", ""))
            enhanced_place["business_status"] = business_status
            enhanced_place["business_hours_note"] = self._get_business_hours_note(business_status, current_hour)
            
            enhanced_places.append(enhanced_place)
        
        return enhanced_places
    
    def _determine_business_status(self, current_hour: int, tags: str) -> str:
        """根据时间和场所类型判断营业状态"""
        tags_lower = tags.lower()
        
        # 不同类型场所的营业时间模式
        if any(keyword in tags_lower for keyword in ["咖啡", "茶", "甜品"]):
            # 咖啡厅、茶馆类：7:00-22:00
            return "营业" if 7 <= current_hour <= 22 else "暂停营业"
        elif any(keyword in tags_lower for keyword in ["餐厅", "饭店", "食堂"]):
            # 餐厅类：11:00-21:00
            return "营业" if 11 <= current_hour <= 21 else "暂停营业"
        elif any(keyword in tags_lower for keyword in ["商场", "购物"]):
            # 商场类：10:00-22:00
            return "营业" if 10 <= current_hour <= 22 else "暂停营业"
        elif any(keyword in tags_lower for keyword in ["图书馆", "书店"]):
            # 图书馆类：9:00-21:00
            return "营业" if 9 <= current_hour <= 21 else "暂停营业"
        else:
            # 默认：9:00-21:00
            return "营业" if 9 <= current_hour <= 21 else "暂停营业"
    
    def _get_business_hours_note(self, status: str, current_hour: int) -> str:
        """获取营业时间说明"""
        if status == "营业":
            if current_hour < 12:
                return "上午营业中"
            elif current_hour < 18:
                return "下午营业中"
            else:
                return "晚间营业中"
        else:
            if current_hour < 8:
                return "尚未开始营业"
            else:
                return "已结束营业"


# 创建全局实例
realtime_service = RealTimeDataService()