/* app.wxss */
page {
  --primary: #4a6fa5;
  --primary-light: #6e8fc5;
  --primary-dark: #305182;
  --secondary: #9dc0e5;
  --light: #f0f5fa;
  --dark: #2c3e50;
  --success: #4a934a;
  --border-radius: 12rpx;
  --box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
  --transition: all 0.3s ease;
  
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
  background-color: var(--light);
  color: var(--dark);
}

.container {
  padding: 30rpx 20rpx;
}

/* 通用卡片样式 */
.card {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 40rpx;
  box-shadow: var(--box-shadow);
  margin-bottom: 30rpx;
}

/* 通用按钮样式 */
.btn {
  background-color: var(--primary);
  color: white;
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  margin-top: 20rpx;
  width: 100%;
  text-align: center;
}

.btn-light {
  background-color: var(--light);
  color: var(--primary);
  border: 2rpx dashed var(--primary-light);
} 