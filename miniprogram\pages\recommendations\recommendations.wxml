<view class="container">
  <view class="header">
    <view class="header-title">最佳会面点推荐</view>
    <view class="header-subtitle">根据您提供的地点，我们推荐了几家适合会面的地点</view>
  </view>
  
  <view class="summary">
    <view class="section-title">推荐摘要</view>
    <view>我们基于{{locations.length}}个地点计算了最佳会面点，并在附近找到了{{places.length}}家适合会面的场所。</view>
    <view class="summary-requirements" wx:if="{{userRequirements}}">
      <text class="label">您的需求：</text>{{userRequirements}}
    </view>
    <view class="summary-requirements" wx:else>
      <text class="label">您的需求：</text>无特殊需求
    </view>
  </view>
  
  <view class="location-info">
    <view class="section-title">地点信息</view>
    <view class="location-table">
      <view class="table-header">
        <view class="table-cell">地点</view>
        <view class="table-cell">详细地址</view>
      </view>
      <block wx:for="{{locations}}" wx:key="index">
        <view class="table-row">
          <view class="table-cell">{{item}}</view>
          <view class="table-cell">{{item}}</view>
        </view>
      </block>
    </view>
  </view>
  
  <view class="section-title">地图展示</view>
  <view class="map-container">
    <map id="map" 
      longitude="{{centerLocation.longitude}}" 
      latitude="{{centerLocation.latitude}}" 
      scale="{{14}}"
      markers="{{markers}}"
      show-location="true">
    </map>
  </view>
  
  <view class="section-title">推荐会面点</view>
  <view class="places-container">
    <block wx:for="{{places}}" wx:key="index">
      <view class="place-card">
        <view class="place-name">{{item.name}}</view>
        <view class="place-info">
          <view class="info-item">
            <text class="label">评分：</text>{{item.rating}}
          </view>
          <view class="info-item">
            <text class="label">地址：</text>{{item.address}}
          </view>
          <view class="info-item">
            <text class="label">营业时间：</text>{{item.business_hours}}
          </view>
          <view class="info-item" wx:if="{{item.phone}}">
            <text class="label">电话：</text>{{item.phone}}
          </view>
          <view class="info-item">
            <text class="label">距离中心点：</text>{{item.distance}}公里
          </view>
          <view class="place-tags" wx:if="{{item.tags}}">
            <text class="place-tag">{{item.tags}}</text>
          </view>
        </view>
        <view class="place-actions">
          <button class="nav-btn" bindtap="openLocation" data-index="{{index}}">导航</button>
          <button class="call-btn" bindtap="makePhoneCall" data-phone="{{item.phone}}" wx:if="{{item.phone}}">拨打电话</button>
        </view>
      </view>
    </block>
  </view>
  
  <view class="transportation">
    <view class="section-title">交通与停车建议</view>
    <view class="transport-card">
      <view class="transport-title">前往方式</view>
      <view>最佳会面点位于{{centerLocation.longitude}}, {{centerLocation.latitude}}附近，各地点前往方式如下：</view>
      <view class="transport-list">
        <block wx:for="{{locations}}" wx:key="index">
          <view class="transport-item">
            <text class="label">{{item}}：</text>距离中心点约4.4公里
          </view>
        </block>
      </view>
      <view class="transport-title">停车建议</view>
      <view class="transport-list">
        <view class="transport-item">大部分推荐的会面点周边有停车场或提供停车服务</view>
        <view class="transport-item">建议使用地图导航到目的地</view>
        <view class="transport-item">高峰时段建议提前30分钟出发，以便寻找停车位</view>
      </view>
    </view>
  </view>
  
  <view class="footer">
    <view>© 2025 最佳会面点推荐 | 数据来源: 高德地图</view>
  </view>
</view> 