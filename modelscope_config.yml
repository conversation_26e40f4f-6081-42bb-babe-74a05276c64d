# ModelScope 项目配置

# 项目基本信息
title: "MeetSpot - 智能会面点推荐系统"
description: "基于 ModelScope MCP 服务的智能会面点推荐系统，集成高德地图 MCP 服务，提供精准的地理编码、POI 搜索和路径规划功能。"
license: "Apache-2.0"
author: "JasonRobert"
tags: ["智能推荐", "MCP服务", "地图应用", "Agent应用", "Gradio"]

# 技术栈
framework: "Gradio"
python_version: "3.8+"
dependencies:
  - gradio>=4.0.0
  - aiohttp>=3.8.0
  - pydantic>=2.0.0
  - toml>=0.10.2
  - loguru>=0.6.0

# 应用配置
app:
  interface_type: "gradio"
  main_file: "app.py"
  port: 7860
  
# 功能特性
features:
  - "AI 增强推荐算法"
  - "精准地理编码"
  - "智能 POI 搜索"
  - "多种路径规划"
  - "服务自动回退"
  - "现代化交互界面"

# 应用场景
use_cases:
  - "商务会面地点推荐"
  - "朋友聚会场所选择"
  - "旅游景点中间点查找"
  - "团队活动地点规划"
