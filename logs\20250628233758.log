2025-06-28 23:38:09.973 | INFO     | __main__:performance_middleware:73 - 处理请求 #1: GET http://127.0.0.1:8000/
2025-06-28 23:38:09.974 | INFO     | __main__:performance_middleware:92 - 请求 #1 完成，耗时: 0.001秒
2025-06-28 23:38:09.989 | INFO     | __main__:performance_middleware:73 - 处理请求 #2: GET http://127.0.0.1:8000/workspace/meetspot_finder.html
2025-06-28 23:38:10.066 | INFO     | __main__:performance_middleware:92 - 请求 #2 完成，耗时: 0.077秒
2025-06-28 23:38:17.666 | INFO     | __main__:performance_middleware:73 - 处理请求 #3: GET http://127.0.0.1:8000/favicon.ico
2025-06-28 23:38:17.667 | INFO     | __main__:performance_middleware:92 - 请求 #3 完成，耗时: 0.001秒
2025-06-28 23:39:46.902 | INFO     | __main__:performance_middleware:73 - 处理请求 #4: POST http://localhost:8000/api/find_meetspot
2025-06-28 23:39:46.905 | INFO     | __main__:find_meetspot:315 - [req_1751125186905] API请求开始: locations=['北京大学', '清华大学'], keywords=咖啡馆
2025-06-28 23:39:47.429 | INFO     | __main__:find_meetspot:371 - [req_1751125186905] API请求完成，耗时: 0.52秒
2025-06-28 23:39:47.429 | INFO     | __main__:performance_middleware:92 - 请求 #4 完成，耗时: 0.528秒
2025-06-28 23:40:03.686 | INFO     | __main__:performance_middleware:73 - 处理请求 #5: POST http://localhost:8000/api/find_meetspot
2025-06-28 23:40:03.687 | INFO     | __main__:find_meetspot:315 - [req_1751125203687] API请求开始: locations=['北京大学', '清华大学'], keywords=奶茶店
2025-06-28 23:40:03.969 | INFO     | __main__:find_meetspot:371 - [req_1751125203687] API请求完成，耗时: 0.28秒
2025-06-28 23:40:03.969 | INFO     | __main__:performance_middleware:92 - 请求 #5 完成，耗时: 0.284秒
2025-06-28 23:40:55.785 | INFO     | __main__:performance_middleware:73 - 处理请求 #6: POST http://localhost:8000/api/find_meetspot
2025-06-28 23:40:55.786 | INFO     | __main__:find_meetspot:315 - [req_1751125255786] API请求开始: locations=['北京大学', '清华大学'], keywords=咖啡馆
2025-06-28 23:40:56.115 | INFO     | __main__:find_meetspot:371 - [req_1751125255786] API请求完成，耗时: 0.33秒
2025-06-28 23:40:56.116 | INFO     | __main__:performance_middleware:92 - 请求 #6 完成，耗时: 0.331秒
2025-06-28 23:41:01.426 | INFO     | __main__:performance_middleware:73 - 处理请求 #7: POST http://localhost:8000/api/find_meetspot
2025-06-28 23:41:01.427 | INFO     | __main__:find_meetspot:315 - [req_1751125261427] API请求开始: locations=['北京大学', '清华大学'], keywords=咖啡馆
2025-06-28 23:41:01.750 | INFO     | __main__:find_meetspot:371 - [req_1751125261427] API请求完成，耗时: 0.32秒
2025-06-28 23:41:01.751 | INFO     | __main__:performance_middleware:92 - 请求 #7 完成，耗时: 0.325秒
2025-06-28 23:44:32.085 | INFO     | __main__:performance_middleware:73 - 处理请求 #8: GET http://localhost:8000/workspace/js_src/place_recommendation_20250628234101_7e786edb.html
2025-06-28 23:44:32.087 | INFO     | __main__:performance_middleware:92 - 请求 #8 完成，耗时: 0.002秒
2025-06-28 23:44:39.598 | INFO     | __main__:performance_middleware:73 - 处理请求 #9: GET http://localhost:8000/favicon.ico
2025-06-28 23:44:39.599 | INFO     | __main__:performance_middleware:92 - 请求 #9 完成，耗时: 0.000秒
2025-06-28 23:45:10.802 | INFO     | __main__:performance_middleware:73 - 处理请求 #10: HEAD http://localhost:8000/
2025-06-28 23:45:10.803 | INFO     | __main__:performance_middleware:92 - 请求 #10 完成，耗时: 0.001秒
2025-06-28 23:45:20.988 | INFO     | __main__:performance_middleware:73 - 处理请求 #11: GET http://localhost:8000/workspace/js_src/place_recommendation_20250628234101_7e786edb.html
2025-06-28 23:45:20.989 | INFO     | __main__:performance_middleware:92 - 请求 #11 完成，耗时: 0.001秒
2025-06-28 23:45:48.856 | INFO     | __main__:performance_middleware:73 - 处理请求 #12: POST http://localhost:8000/api/find_meetspot
2025-06-28 23:45:48.857 | INFO     | __main__:find_meetspot:315 - [req_1751125548857] API请求开始: locations=['北京大学', '清华大学'], keywords=咖啡馆
2025-06-28 23:45:49.165 | INFO     | __main__:find_meetspot:371 - [req_1751125548857] API请求完成，耗时: 0.31秒
2025-06-28 23:45:49.166 | INFO     | __main__:performance_middleware:92 - 请求 #12 完成，耗时: 0.310秒
2025-06-28 23:45:50.488 | INFO     | __main__:performance_middleware:73 - 处理请求 #13: GET http://localhost:8000/workspace/js_src/place_recommendation_20250628234549_c86bf036.html
2025-06-28 23:45:50.490 | INFO     | __main__:performance_middleware:92 - 请求 #13 完成，耗时: 0.002秒
2025-06-28 23:45:54.177 | INFO     | __main__:performance_middleware:73 - 处理请求 #14: GET http://localhost:8000/favicon.ico
2025-06-28 23:45:54.178 | INFO     | __main__:performance_middleware:92 - 请求 #14 完成，耗时: 0.001秒
2025-06-28 23:46:20.150 | INFO     | __main__:performance_middleware:73 - 处理请求 #15: POST http://localhost:8000/api/find_meetspot
2025-06-28 23:46:20.151 | INFO     | __main__:find_meetspot:315 - [req_1751125580151] API请求开始: locations=['北京大学', '清华大学'], keywords=咖啡馆
2025-06-28 23:46:20.451 | INFO     | __main__:find_meetspot:371 - [req_1751125580151] API请求完成，耗时: 0.30秒
2025-06-28 23:46:20.451 | INFO     | __main__:performance_middleware:92 - 请求 #15 完成，耗时: 0.301秒
2025-06-28 23:46:20.874 | INFO     | __main__:performance_middleware:73 - 处理请求 #16: GET http://localhost:8000/workspace/js_src/place_recommendation_20250628234620_41eb251c.html
2025-06-28 23:46:20.876 | INFO     | __main__:performance_middleware:92 - 请求 #16 完成，耗时: 0.002秒
2025-06-29 00:17:03.557 | INFO     | __main__:performance_middleware:73 - 处理请求 #17: POST http://localhost:8000/api/find_meetspot
2025-06-29 00:17:03.559 | INFO     | __main__:find_meetspot:315 - [req_1751127423559] API请求开始: locations=['北京大学', '清华大学'], keywords=奶茶店
2025-06-29 00:17:03.861 | INFO     | __main__:find_meetspot:371 - [req_1751127423559] API请求完成，耗时: 0.30秒
2025-06-29 00:17:03.862 | INFO     | __main__:performance_middleware:92 - 请求 #17 完成，耗时: 0.305秒
2025-06-29 00:17:05.054 | INFO     | __main__:performance_middleware:73 - 处理请求 #18: GET http://localhost:8000/workspace/js_src/place_recommendation_20250629001703_72f277a4.html
2025-06-29 00:17:05.059 | INFO     | __main__:performance_middleware:92 - 请求 #18 完成，耗时: 0.005秒
2025-06-29 00:18:00.003 | INFO     | __main__:performance_middleware:73 - 处理请求 #19: POST http://localhost:8000/api/find_meetspot
2025-06-29 00:18:00.006 | INFO     | __main__:find_meetspot:315 - [req_1751127480006] API请求开始: locations=['北京大学', '清华大学'], keywords=奶茶店
2025-06-29 00:18:00.356 | INFO     | __main__:find_meetspot:371 - [req_1751127480006] API请求完成，耗时: 0.35秒
2025-06-29 00:18:00.357 | INFO     | __main__:performance_middleware:92 - 请求 #19 完成，耗时: 0.354秒
2025-06-29 00:18:02.864 | INFO     | __main__:performance_middleware:73 - 处理请求 #20: GET http://localhost:8000/workspace/js_src/place_recommendation_20250629001800_1a192383.html
2025-06-29 00:18:02.867 | INFO     | __main__:performance_middleware:92 - 请求 #20 完成，耗时: 0.004秒
2025-06-29 00:18:52.931 | INFO     | __main__:performance_middleware:73 - 处理请求 #21: POST http://localhost:8000/api/find_meetspot
2025-06-29 00:18:52.934 | INFO     | __main__:find_meetspot:315 - [req_1751127532934] API请求开始: locations=['北京大学', '清华大学'], keywords=奶茶店
2025-06-29 00:18:53.277 | INFO     | __main__:find_meetspot:371 - [req_1751127532934] API请求完成，耗时: 0.34秒
2025-06-29 00:18:53.278 | INFO     | __main__:performance_middleware:92 - 请求 #21 完成，耗时: 0.347秒
2025-06-29 00:18:54.334 | INFO     | __main__:performance_middleware:73 - 处理请求 #22: GET http://localhost:8000/workspace/js_src/place_recommendation_20250629001853_af8064e6.html
2025-06-29 00:18:54.338 | INFO     | __main__:performance_middleware:92 - 请求 #22 完成，耗时: 0.003秒
