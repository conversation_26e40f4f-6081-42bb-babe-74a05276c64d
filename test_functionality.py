#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MeetSpot 功能测试脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_mcp_connection():
    """测试MCP连接"""
    print("🔍 测试MCP连接...")
    
    try:
        from app.mcp.amap_mcp_client import AmapMCPService
        
        # 测试MCP服务
        mcp_service = AmapMCPService(use_official=False)
        success = await mcp_service.initialize()
        
        if success:
            print("✅ MCP服务连接成功")
            
            # 测试地理编码
            print("\n🔍 测试地理编码...")
            geocode_result = await mcp_service.geocode("北京市海淀区中关村")
            if geocode_result.get("error"):
                print(f"❌ 地理编码失败: {geocode_result['error']}")
            else:
                print(f"✅ 地理编码成功: {geocode_result}")
            
            # 测试周边搜索
            print("\n🔍 测试周边搜索...")
            search_result = await mcp_service.search_around("116.397428,39.90923", "咖啡馆", "3000")
            if search_result.get("error"):
                print(f"❌ 周边搜索失败: {search_result['error']}")
            else:
                pois = search_result.get("result", {}).get("pois", [])
                print(f"✅ 周边搜索成功，找到 {len(pois)} 个咖啡馆")
        else:
            print("❌ MCP服务连接失败")
            
    except Exception as e:
        print(f"❌ MCP测试异常: {e}")
        import traceback
        traceback.print_exc()

async def test_recommender():
    """测试推荐器"""
    print("\n🔍 测试推荐器...")
    
    try:
        from app.tool.meetspot_recommender import CafeRecommender
        
        recommender = CafeRecommender()
        result = await recommender.execute(
            locations=["北京市海淀区中关村", "北京市朝阳区三里屯"],
            keywords="咖啡馆",
            user_requirements="停车方便"
        )
        
        if result.success:
            print("✅ 推荐器测试成功")
            print(f"推荐结果: {result.output[:200]}...")
        else:
            print(f"❌ 推荐器测试失败: {result.error}")
            
    except Exception as e:
        print(f"❌ 推荐器测试异常: {e}")
        import traceback
        traceback.print_exc()

async def test_gradio_app():
    """测试Gradio应用"""
    print("\n🔍 测试Gradio应用...")
    
    try:
        from app import create_app
        
        app = create_app()
        if app:
            print("✅ Gradio应用创建成功")
            print(f"应用类型: {type(app)}")
        else:
            print("❌ Gradio应用创建失败")
            
    except Exception as e:
        print(f"❌ Gradio应用测试异常: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主测试函数"""
    print("🚀 开始MeetSpot功能测试...\n")
    
    # 测试MCP连接
    await test_mcp_connection()
    
    # 测试推荐器
    await test_recommender()
    
    # 测试Gradio应用
    await test_gradio_app()
    
    print("\n🏁 测试完成!")

if __name__ == "__main__":
    asyncio.run(main())