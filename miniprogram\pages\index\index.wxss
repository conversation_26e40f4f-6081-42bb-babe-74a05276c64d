/* index.wxss */
.logo {
  text-align: center;
  margin-bottom: 40rpx;
}

.logo-title {
  font-size: 60rpx;
  margin: 0;
  color: var(--primary);
  font-weight: bold;
}

.logo-subtitle {
  font-size: 28rpx;
  margin-top: 10rpx;
  color: var(--primary-light);
}

.search-card {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 40rpx;
  box-shadow: var(--box-shadow);
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 36rpx;
  color: var(--primary-dark);
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
}

.section-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0Ij48cGF0aCBmaWxsPSJub25lIiBkPSJNMCAwaDI0djI0SDB6Ii8+PHBhdGggZD0iTTExIDE0di0zaDJ2M2g1di0yaDJ2MmgtMnYyaC0ydi0yaC01em0tNi03aDJ2Mmg5di0yaDJ2Mmgtd12h2di0yaC01djVoMTFWN2gtMTVWNXYyeiIgZmlsbD0icmdiYSg3NCwxMTEsMTY1LDEpIi8+PC9zdmc+');
  background-size: cover;
}

.form-group {
  margin-bottom: 25rpx;
}

.label {
  display: block;
  margin-bottom: 16rpx;
  color: var(--dark);
  font-weight: 600;
  font-size: 28rpx;
}

input, textarea {
  width: 100%;
  padding: 24rpx 30rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.location-inputs {
  margin-bottom: 12rpx;
  position: relative;
  display: flex;
  align-items: center;
}

.location-icon {
  position: absolute;
  left: 12rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 32rpx;
  height: 32rpx;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0Ij48cGF0aCBmaWxsPSJub25lIiBkPSJNMCAwaDI0djI0SDB6Ii8+PHBhdGggZD0iTTEyIDIwLjlsNC45NS00Ljk1YTcgNyAwIDEgMC05LjkgMEwxMiAyMC45em0wIDIuODNsLTYuMzYtNi4zNmE5IDkgMCAxIDEgMTIuNzIgMEwxMiAyMy43M3oiIGZpbGw9InJnYmEoMTUzLDE1MywxNTMsMSkiLz48L3N2Zz4=');
  background-size: cover;
}

.location-inputs input {
  padding-left: 50rpx;
  flex: 1;
}

.location-remove {
  width: 44rpx;
  height: 44rpx;
  margin-left: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 32rpx;
}

.place-type-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.place-type-option {
  display: inline-flex;
  align-items: center;
  background-color: var(--light);
  border: 2rpx solid transparent;
  border-radius: 16rpx;
  padding: 16rpx 30rpx;
  transition: var(--transition);
}

.place-type-option.selected {
  border-color: var(--primary);
  background-color: rgba(74, 111, 165, 0.1);
}

.place-type-name {
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 28rpx;
}

.place-type-icon {
  width: 32rpx;
  height: 32rpx;
  background-size: cover;
}

.place-type-icon.cafe {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0Ij48cGF0aCBmaWxsPSJub25lIiBkPSJNMCAwaDI0djI0SDB6Ii8+PHBhdGggZD0iTTIgMTloMThWOEgydjExem0xNi0xMzVoMnYzaC0yVjZ6TTYgNmgydjNoLTJWNnptMTQgMTRIMnYyaDE4di0yeiIgZmlsbD0icmdiYSg3NCwxMTEsMTY1LDEpIi8+PC9zdmc+');
}

.place-type-icon.restaurant {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0Ij48cGF0aCBmaWxsPSJub25lIiBkPSJNMCAwaDI0djI0SDB6Ii8+PHBhdGggZD0iTTkgMTMuOTVWMTBoMXYzLjk1bDMuMDUgMy4wNS0uNy43TDkgMTQuMzVsLTMuMzUgMy4zNS0uNy0uN0w5IDEzLjk1ek00IDExYTggOCAwIDEgMSAxNiAwIDggOCAwIDAgMS0xNiAwem04LTEwQzUuMzcgMSAwIDYuMzcgMCAxMnM1LjM3IDExIDEyIDExIDEyLTUuMzcgMTItMTJTMTguNjMgMSAxMiAxeiIgZmlsbD0icmdiYSg3NCwxMTEsMTY1LDEpIi8+PC9zdmc+');
}

.place-type-icon.library {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0Ij48cGF0aCBmaWxsPSJub25lIiBkPSJNMCAwaDI0djI0SDB6Ii8+PHBhdGggZD0iTTEzIDIxdi0yaDh2LTJoLTh2LTJoLTJ2MkgzdjJoOHYyaDJ6TTMgNWgydjJIM3YyaDJWN2g4VjVoMnYyaDJWNWgydjJoLTJ2Mmgydi0yaDJWNWgtNlYzaC0ydjJIOXYySDdWNUgzeiIgZmlsbD0icmdiYSg3NCwxMTEsMTY1LDEpIi8+PC9zdmc+');
}

.place-type-icon.mall {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0Ij48cGF0aCBmaWxsPSJub25lIiBkPSJNMCAwaDI0djI0SDB6Ii8+PHBhdGggZD0iTTEyIDIyQzYuNDc3IDIyIDIgMTcuNTIzIDIgMTJTNi40NzcgMiAxMiAyczEwIDQuNDc3IDEwIDEwLTQuNDc3IDEwLTEwIDEwem0wLTJhOCA4IDAgMSAwIDAtMTYgOCA4IDAgMCAwIDAgMTZ6bS01LTdoMlY5aDJWN2gtMlY1SDd2MmgydjR6bTggNGg0di0yYy0uNTUyIDAtMSAwLTEtLjV2LTNjMC0uODQtLjg5NS0xLjUtMi0xLjUtLjUzMyAwLTEuMDMzLjE5Ni0xLjQxNC41NTRsMS40MTQgMS40MTRDMTQuMDkgMTAuMTk2IDE0LjUzMyAxMCAxNSAxMHYxaC0xdjJoMXYxeiIgZmlsbD0icmdiYSg3NCwxMTEsMTY1LDEpIi8+PC9zdmc+');
}

.place-type-icon.park {
  background-image: url('data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');
}

.tip-card {
  background-color: #fff3e0;
  border-left: 4rpx solid var(--primary);
  padding: 30rpx 40rpx;
  margin: 30rpx 0;
  border-radius: 12rpx;
}

.tip-text {
  margin: 0;
  color: #795548;
  font-size: 28rpx;
}

.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 20rpx;
  margin-top: 40rpx;
}

.feature-card {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 25rpx;
  box-shadow: var(--box-shadow);
  text-align: center;
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 30rpx;
  background-size: cover;
}

.feature-icon.location-analysis {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0Ij48cGF0aCBmaWxsPSJub25lIiBkPSJNMCAwaDI0djI0SDB6Ii8+PHBhdGggZD0iTTEyIDIwLjlsNC45NS00Ljk1YTcgNyAwIDEgMC05LjkgMEwxMiAyMC45em0wIDIuODNsLTYuMzYtNi4zNmE5IDkgMCAxIDEgMTIuNzIgMEwxMiAyMy43M3oiIGZpbGw9InJnYmEoNzQsMTExLDE2NSwxKSIvPjwvc3ZnPg==');
}

.feature-icon.place-variety {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0Ij48cGF0aCBmaWxsPSJub25lIiBkPSJNMCAwaDI0djI0SDB6Ii8+PHBhdGggZD0iTTEzIDIxdi0yaDh2LTJoLTh2LTJoLTJ2MkgzdjJoOHYyaDJ6TTMgNWgydjJIM3YyaDJWN2g4VjVoMnYyaDJWNWgydjJoLTJ2Mmgydi0yaDJWNWgtNlYzaC0ydjJIOXYySDdWNUgzeiIgZmlsbD0icmdiYSg3NCwxMTEsMTY1LDEpIi8+PC9zdmc+');
}

.feature-icon.interactive-map {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0Ij48cGF0aCBmaWxsPSJub25lIiBkPSJNMCAwaDI0djI0SDB6Ii8+PHBhdGggZD0iTTIgNWw3LTMgNiAzIDYtMyAxIDEtNyAzLTYtMy02IDN6TTkgMTcjMTUgMTBsLTYtM3Y3bC0yIDFWNWw3LTMgNiAzIDYtM3YxMmwtNiAzLTYtM3oiIGZpbGw9InJnYmEoNzQsMTExLDE2NSwxKSIvPjwvc3ZnPg==');
}

.feature-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
  color: var(--primary-dark);
}

.feature-text {
  font-size: 24rpx;
  color: #666;
} 