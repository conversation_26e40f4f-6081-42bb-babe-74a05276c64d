#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration management for MeetSpot application
"""

import os
import toml
from pathlib import Path
from typing import Any, Dict, Optional
from pydantic import BaseModel


class AmapConfig(BaseModel):
    """Amap API configuration"""
    api_key: str = ""
    web_api_key: str = ""
    security_js_code: str = ""


class MCPConfig(BaseModel):
    """MCP service configuration"""
    amap_sse_url: str = ""
    use_official_mcp: bool = False


class LLMConfig(BaseModel):
    """LLM configuration"""
    model: str = "Qwen/Qwen2.5-72B-Instruct"
    base_url: str = "https://api.siliconflow.cn/v1"
    api_key: str = ""
    max_tokens: int = 4096
    temperature: float = 0.7
    api_type: str = "Openai"
    api_version: str = ""


class ServerConfig(BaseModel):
    """Server configuration"""
    host: str = "127.0.0.1"
    port: int = 8000


class Config(BaseModel):
    """Main configuration class"""
    amap: AmapConfig = AmapConfig()
    mcp: MCPConfig = MCPConfig()
    llm: LLMConfig = LLMConfig()
    server: ServerConfig = ServerConfig()


class ConfigManager:
    """Configuration manager"""
    
    def __init__(self, config_file: str = "config/config.toml"):
        self.config_file = Path(config_file)
        self._config = self._load_config()
    
    def _load_config(self) -> Config:
        """Load configuration from TOML file"""
        if not self.config_file.exists():
            # Create default config if file doesn't exist
            default_config = Config()
            self._save_config(default_config)
            return default_config
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = toml.load(f)
            return Config(**data)
        except Exception as e:
            print(f"Error loading config: {e}")
            return Config()
    
    def _save_config(self, config: Config):
        """Save configuration to TOML file"""
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            toml.dump(config.dict(), f)
    
    @property
    def config(self) -> Config:
        """Get current configuration"""
        return self._config
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if hasattr(value, k):
                value = getattr(value, k)
            else:
                return default
        
        return value
    
    def reload(self):
        """Reload configuration from file"""
        self._config = self._load_config()


# Global configuration instance
config = ConfigManager()

# For backward compatibility
def get_config():
    return config.config