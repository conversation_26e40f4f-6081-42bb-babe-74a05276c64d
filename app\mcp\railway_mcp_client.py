# app/mcp/railway_mcp_client.py
import asyncio
import json
import aiohttp
import uuid
import re
from typing import Dict, Any, List, Optional
from app.logger import logger

class Railway12306MCPClient:
    """12306 铁路 MCP 服务客户端 (基于SSE会话)"""
    
    def __init__(self):
        self.base_url = "https://mcp.api-inference.modelscope.net/db043039d3ad4f"
        self.sse_url = f"{self.base_url}/sse"
        self.session = None
        self.connected = False
        self.session_id = None
        self.message_endpoint = None
        self.request_id_counter = 1
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            headers={
                "Accept": "text/event-stream",
                "Cache-Control": "no-cache",
                "Connection": "keep-alive"
            },
            timeout=aiohttp.ClientTimeout(total=60, connect=30)
        )
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.session:
            await self.session.close()
    
    async def connect(self) -> bool:
        """连接到 12306 MCP 服务并获取会话端点"""
        try:
            # 连接SSE端点获取会话信息
            async with self.session.get(self.sse_url) as response:
                if response.status == 200:
                    # 读取SSE事件流获取端点信息
                    async for line in response.content:
                        line_str = line.decode('utf-8').strip()
                        
                        if line_str.startswith('event: endpoint'):
                            continue
                        elif line_str.startswith('data: '):
                            endpoint_data = line_str[6:]  # 移除 'data: ' 前缀
                            
                            # 解析端点信息 /messages/?session_id=xxx
                            if '/messages/' in endpoint_data and 'session_id=' in endpoint_data:
                                self.message_endpoint = f"{self.base_url}{endpoint_data}"
                                # 提取session_id
                                match = re.search(r'session_id=([a-f0-9]+)', endpoint_data)
                                if match:
                                    self.session_id = match.group(1)
                                    self.connected = True
                                    logger.info(f"✅ 成功连接到 12306 铁路 MCP 服务，会话ID: {self.session_id}")
                                    return True
                            break
                    
                    if not self.connected:
                        logger.warning("⚠️ 未能获取有效的12306 MCP会话端点")
                        return False
                else:
                    logger.warning(f"⚠️ 12306 MCP SSE 服务连接失败 (HTTP {response.status})")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 连接 12306 MCP 服务异常: {e}")
            return False
    
    async def send_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """发送消息到12306 MCP会话端点"""
        if not self.connected or not self.message_endpoint:
            raise ValueError("12306 MCP 服务未连接或缺少消息端点")
        
        try:
            # 构建消息请求
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            async with self.session.post(
                self.message_endpoint,
                json=message,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"✅ 12306 MCP 消息发送成功")
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"❌ 12306 MCP 消息发送失败: HTTP {response.status}: {error_text}")
                    return {"error": f"HTTP {response.status}: {error_text}"}
                    
        except Exception as e:
            logger.error(f"❌ 12306 MCP 消息发送异常: {e}")
            return {"error": str(e)}
    
    async def call_tool_session(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """通过会话调用 12306 MCP 工具"""
        if not self.connected:
            raise ValueError("12306 MCP 服务未连接")
        
        try:
            # 构建工具调用消息
            message = {
                "jsonrpc": "2.0",
                "id": self.request_id_counter,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            self.request_id_counter += 1
            
            return await self.send_message(message)
                    
        except Exception as e:
            logger.error(f"❌ 12306 MCP 工具调用异常: {tool_name}, 错误: {e}")
            return {"error": str(e)}
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用 12306 MCP 工具 (兼容接口)"""
        return await self.call_tool_session(tool_name, arguments)
    
    async def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用的 12306 工具列表"""
        try:
            message = {
                "jsonrpc": "2.0",
                "id": self.request_id_counter,
                "method": "tools/list",
                "params": {}
            }
            self.request_id_counter += 1
            
            result = await self.send_message(message)
            
            # 解析工具列表
            tools = []
            if result and "result" in result:
                result_data = result["result"]
                if "tools" in result_data:
                    tools = result_data["tools"]
            
            logger.info(f"📋 获取到 12306 MCP 工具: {len(tools)} 个")
            return tools
                    
        except Exception as e:
            logger.error(f"❌ 获取 12306 工具列表异常: {e}")
            return []

class Railway12306MCPService:
    """12306 铁路 MCP 服务包装器"""
    
    def __init__(self):
        self.client = Railway12306MCPClient()
        self.available_tools = []
    
    async def initialize(self) -> bool:
        """初始化 12306 服务"""
        async with self.client as client:
            self.available_tools = await client.get_available_tools()
            success = len(self.available_tools) > 0
            
            if success:
                logger.info(f"🚀 12306 铁路 MCP 服务初始化成功，可用工具: {len(self.available_tools)} 个")
            else:
                logger.warning("⚠️ 12306 铁路 MCP 服务初始化失败")
            
            return success
    
    async def get_current_date(self) -> Dict[str, Any]:
        """获取当前日期"""
        async with self.client as client:
            return await client.call_tool_session(
                "mcp_12306-mcp_get-current-date",
                {}
            )
    
    async def get_station_code_by_names(self, station_names: str) -> Dict[str, Any]:
        """通过车站名获取站点编码"""
        async with self.client as client:
            return await client.call_tool_session(
                "mcp_12306-mcp_get-station-code-by-names",
                {
                    "stationNames": station_names
                }
            )
    
    async def get_station_code_of_citys(self, citys: str) -> Dict[str, Any]:
        """通过城市名获取站点编码"""
        async with self.client as client:
            return await client.call_tool_session(
                "mcp_12306-mcp_get-station-code-of-citys",
                {
                    "citys": citys
                }
            )
    
    async def get_tickets(self, date: str, from_station: str, to_station: str, 
                         train_filter_flags: str = "", sort_flag: str = "", 
                         sort_reverse: bool = False, limited_num: int = 10) -> Dict[str, Any]:
        """查询余票信息"""
        async with self.client as client:
            return await client.call_tool_session(
                "mcp_12306-mcp_get-tickets",
                {
                    "date": date,
                    "fromStation": from_station,
                    "toStation": to_station,
                    "trainFilterFlags": train_filter_flags,
                    "sortFlag": sort_flag,
                    "sortReverse": sort_reverse,
                    "limitedNum": limited_num
                }
            )
    
    async def get_interline_tickets(self, date: str, from_station: str, to_station: str,
                                  middle_station: str = "", show_wz: bool = False,
                                  limited_num: int = 10) -> Dict[str, Any]:
        """查询中转余票信息"""
        async with self.client as client:
            return await client.call_tool_session(
                "mcp_12306-mcp_get-interline-tickets",
                {
                    "date": date,
                    "fromStation": from_station,
                    "toStation": to_station,
                    "middleStation": middle_station,
                    "showWZ": show_wz,
                    "limitedNum": limited_num
                }
            )
